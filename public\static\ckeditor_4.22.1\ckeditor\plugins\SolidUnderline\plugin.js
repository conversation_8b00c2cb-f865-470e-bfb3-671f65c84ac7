/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-12-21 20:38:29
 * @LastEditors: l<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-04-08 14:59:46
 */
/**
 * 自定义下划线
 */
CKEDITOR.plugins.add("SolidUnderline", {
  init: function (editor) {
    //增加数据过滤器规则
    editor.dataProcessor.dataFilter.addRules({
      elements: {
        $: function (el) {
          try {
            //兼容学科网填空题标签
            if (el.attributes && el.attributes["class"]) {
              if (el.attributes["class"].split(/\s+/).indexOf("qml-bk") !== -1) {
                var newEl = new CKEDITOR.htmlParser.element("u", {});
                el.children &&
                  el.children.forEach(function (child) {
                    newEl.add(child);
                  });
                return newEl;
              }
            }
          } catch (e) {
            console.warn("未找到匹配标签内容");
          }
          return el;
        },
      },
      text: function (text, node) {
        try {
          if (node.parent.attributes.class == "ck-math-tex") return text;
        } catch (e) {
          console.warn("未找到匹配标签内容");
        }

        // 使用正则表达式匹配连续的下划线字符
        return text.replace(/(_+)/g, function (match, p1) {
          // 将匹配到的下划线字符串包裹在 <u> 标签中
          return "<u>" + Array(p1.length).fill("&ensp;").join("") + "</u>";
        });
      },
    });
    // 添加键盘输入监听器
    editor.on("key", function (event) {
      // 只处理 "_"(下划线)键的按下事件
      if (event.data.domEvent.$.code != "Minus") {
        return;
      }

      var ele = editor.getSelection().getRanges()[0].endContainer.$;
      if (ele.parentElement.tagName == "U") {
        ele.parentElement.insertAdjacentHTML("beforeend", "&ensp; ");
      } else if (ele.tagName == "U") {
        ele.insertAdjacentHTML("beforeend", "&ensp; ");
      } else {
        ele.insertAdjacentHTML("beforeend", "<u>&ensp; </u>");
      }

      // 阻止 CKEditor 默认处理下划线键事件
      event.cancel();
    });
    editor.ui.addButton("SolidUnderline", {
      label: "插入下划线",
      icon: this.path + "icon.png",
      command: "SolidUnderline",
      click: function (e) {
        var ele = editor.getSelection().getRanges()[0].endContainer
          .$.parentElement;

        if (ele.tagName == "U") {
          ele.insertAdjacentHTML(
            "beforeend",
            "&ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; "
          );
        } else {
          e.insertHtml(
            "<u>&ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; &ensp; </u>"
          );
        }
      },
    });
    editor.ui.addButton("SpaceLine", {
      label: "插入空格",
      icon: this.path + "space.png",
      command: "SpaceLine",
      click: function (e) {
        e.insertHtml("<span>&ensp;&ensp;&ensp;&ensp;</span>");
      },
    });
  },
});
