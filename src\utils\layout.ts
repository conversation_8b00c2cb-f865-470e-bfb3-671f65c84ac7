/*
 * @Description: 布局工具类 - 统一处理各种页面布局逻辑
 * @Author: 优化重构
 * @Date: 2024-12-19
 */

import { IPAGELAYOUT } from '@/typings/card';
import { PaperConstant } from '@/views/paper.constant';

/**
 * 布局配置接口
 */
export interface ILayoutConfig {
  step: number;
  tagMap: string[];
  adjustPage?: (pageNum: number) => number;
  columnCount?: number; // 栏数
  isA4TwoColumn?: boolean; // 是否为A4两栏布局
}

/**
 * 页面宽度配置接口
 */
export interface IPageWidthConfig {
  width: string;
  condition?: (pageSize: number) => boolean;
}

/**
 * 布局工具类
 */
export class LayoutUtils {
  /**
   * 布局配置映射表
   */
  private static readonly LAYOUT_CONFIGS: Record<IPAGELAYOUT, ILayoutConfig> = {
    [IPAGELAYOUT.A4]: {
      step: 2,
      tagMap: ['反面', '正面'],
      columnCount: 1
    },
    [IPAGELAYOUT.A4_TWO_COLUMN]: {
      step: 4,
      tagMap: ['反面2', '正面1', '正面2', '反面1'],
      columnCount: 2,
      isA4TwoColumn: true
    },
    [IPAGELAYOUT.A3]: {
      step: 4,
      tagMap: ['反面2', '正面1', '正面2', '反面1'],
      columnCount: 2
    },
    [IPAGELAYOUT.A33]: {
      step: 6,
      tagMap: ['反面3', '正面1', '正面2', '正面3', '反面1', '反面2'],
      columnCount: 3
    },
    [IPAGELAYOUT.A32]: {
      step: 5,
      tagMap: ['反面2', '正面1', '正面2', '正面3', '反面1'],
      columnCount: 3
    },
    [IPAGELAYOUT.A23]: {
      step: 5,
      tagMap: ['反面3', '正面1', '正面2', '反面1', '反面2'],
      columnCount: 2,
      adjustPage: (pageNum: number) => {
        const groupSize = pageNum % 5 <= 3 || pageNum % 5 !== 0 ? 2.5 : 2;
        return Math.ceil(pageNum / groupSize);
      },
    },
  };

  /**
   * 页面宽度配置映射表
   */
  private static readonly PAGE_WIDTH_CONFIGS: Record<IPAGELAYOUT, IPageWidthConfig[]> = {
    [IPAGELAYOUT.A4]: [
      { width: PaperConstant.WIDTH_A4 }
    ],
    [IPAGELAYOUT.A4_TWO_COLUMN]: [
      { width: PaperConstant.WIDTH_A4 }  // A4两栏使用A4宽度，内容通过CSS分栏
    ],
    [IPAGELAYOUT.A3]: [
      { width: PaperConstant.WIDTH_A4 }
    ],
    [IPAGELAYOUT.A33]: [
      { width: PaperConstant.WIDTH_A33 }
    ],
    [IPAGELAYOUT.A32]: [
      {
        width: PaperConstant.WIDTH_A33,
        condition: (pageSize: number) => [1, 2, 3].includes(pageSize % 5)
      },
      {
        width: PaperConstant.WIDTH_A4,
        condition: (pageSize: number) => ![1, 2, 3].includes(pageSize % 5)
      }
    ],
    [IPAGELAYOUT.A23]: [
      {
        width: PaperConstant.WIDTH_A33,
        condition: (pageSize: number) => [3, 4, 0].includes(pageSize % 5)
      },
      {
        width: PaperConstant.WIDTH_A4,
        condition: (pageSize: number) => ![3, 4, 0].includes(pageSize % 5)
      }
    ],
  };

  /**
   * 获取布局配置
   * @param layout 页面布局类型
   * @returns 布局配置对象
   */
  static getLayoutConfig(layout: IPAGELAYOUT): ILayoutConfig | null {
    return this.LAYOUT_CONFIGS[layout] || null;
  }

  /**
   * 获取布局步进数
   * @param layout 页面布局类型
   * @returns 步进数
   */
  static getLayoutStep(layout: IPAGELAYOUT): number {
    const config = this.getLayoutConfig(layout);
    return config?.step || 2;
  }

  /**
   * 获取页面宽度
   * @param layout 页面布局类型
   * @param pageSize 当前页码
   * @returns 页面宽度字符串
   */
  static getPageWidth(layout: IPAGELAYOUT, pageSize: number = 1): string {
    const configs = this.PAGE_WIDTH_CONFIGS[layout];
    if (!configs) return '';

    // 查找匹配条件的配置
    for (const config of configs) {
      if (!config.condition || config.condition(pageSize)) {
        return config.width;
      }
    }

    return configs[0]?.width || '';
  }

  /**
   * 计算页面标签文本
   * @param layout 页面布局类型
   * @param pageNum 页码
   * @returns 页面标签文本
   */
  static getPageText(layout: IPAGELAYOUT, pageNum: number): string {
    const config = this.getLayoutConfig(layout);
    if (!config) return '';

    const indexInStep = pageNum % config.step;
    const pageTag = config.tagMap[indexInStep] || '';
    return `第${Math.ceil(pageNum / config.step)}张纸${pageTag}`;
  }

  /**
   * 计算实际页码
   * @param layout 页面布局类型
   * @param pageNum 当前页码
   * @returns 实际页码
   */
  static calculateActualPage(layout: IPAGELAYOUT, pageNum: number): number {
    const config = this.getLayoutConfig(layout);
    if (!config) return pageNum;

    if (layout === IPAGELAYOUT.A32) {
      return pageNum % config.step === 3
        ? Math.ceil(pageNum / (config.step / 2)) - 1
        : Math.ceil(pageNum / (config.step / 2));
    }

    if (config.adjustPage) {
      return config.adjustPage(pageNum);
    }

    return Math.ceil(pageNum / (config.step / 2));
  }

  /**
   * 计算最终步进数（用于页面填充逻辑）
   * @param layout 页面布局类型
   * @returns 最终步进数
   */
  static getFinalStep(layout: IPAGELAYOUT): number {
    const step = this.getLayoutStep(layout);
    
    switch (layout) {
      case IPAGELAYOUT.A23:
        return 2;
      case IPAGELAYOUT.A32:
        return 3;
      default:
        return step / 2;
    }
  }

  /**
   * 判断是否需要创建页头信息
   * @param layout 页面布局类型
   * @param pageSize 当前页码
   * @returns 是否需要创建页头信息
   */
  static shouldCreateHeaderInfo(layout: IPAGELAYOUT, pageSize: number): boolean {
    const step = this.getLayoutStep(layout);

    return (
      (layout === IPAGELAYOUT.A4 && pageSize % 2 === 1) ||
      (layout === IPAGELAYOUT.A4_TWO_COLUMN && pageSize % 4 === 1) ||
      (layout === IPAGELAYOUT.A3 && pageSize % 4 === 1) ||
      (layout === IPAGELAYOUT.A33 && pageSize % 6 === 1) ||
      (layout === IPAGELAYOUT.A32 && pageSize % 5 === 1) ||
      (layout === IPAGELAYOUT.A23 && pageSize % 5 === 1)
    );
  }

  /**
   * 判断是否为A4两栏布局
   * @param layout 页面布局类型
   * @returns 是否为A4两栏布局
   */
  static isA4TwoColumn(layout: IPAGELAYOUT): boolean {
    const config = this.getLayoutConfig(layout);
    return config?.isA4TwoColumn || false;
  }

  /**
   * 获取栏数
   * @param layout 页面布局类型
   * @returns 栏数
   */
  static getColumnCount(layout: IPAGELAYOUT): number {
    const config = this.getLayoutConfig(layout);
    return config?.columnCount || 1;
  }
}
