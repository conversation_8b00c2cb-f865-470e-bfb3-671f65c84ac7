{"name": "project-card", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode dev", "dev:prod": "vue-cli-service serve --mode prod", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode prod", "build:yc": "vue-cli-service build --mode prodyc", "deploy:test": "npm run build:test && node deploy/deploy.js dev", "pre": "vue-cli-service build --mode pre", "lint": "vue-cli-service lint"}, "dependencies": {"moment": "^2.29.3", "@tinymce/tinymce-vue": "^4.0.5", "@types/jquery": "^3.5.29", "ali-oss": "^6.17.1", "babel-polyfill": "^6.26.0", "browser-md5-file": "^1.1.1", "compression-webpack-plugin": "^5.0.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "element-plus": "^2.5.1", "i": "^0.3.6", "jquery": "^3.6.0", "js-md5": "^0.7.3", "mitt": "^3.0.1", "postcss-loader": "^8.1.1", "qrcode": "^1.5.3", "spark-md5": "^3.0.1", "tinymce": "^5.10.2", "vue": "3.2.36", "vue-router": "^4.0.0-0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0"}, "devDependencies": {"archiver": "^7.0.1", "ssh2-sftp-client": "^10.0.3", "node-ssh": "^13.2.0", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "3.2.36", "@vue/eslint-config-typescript": "^5.0.2", "autoprefixer": "^10.4.19", "axios": "^0.21.0", "eslint": "^6.7.2", "eslint-plugin-vue": "9.2.0", "fork-ts-checker-webpack-plugin": "^6.0.7", "postcss": "^8.4.38", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "typescript": "~4.3.5", "vue-loader-v16": "^16.0.0-beta.5.4"}}