<template>
  <!-- is-objective: 客观题 -->
  <div ref="quesCardRef" class="q-opt card is-objective is-static-ques" :id="qId" :data-typeid="item.typeId"
    :style="{ fontSize: Paper.fontSize, fontFamily: Paper.fontFamily }"
    :class="{ onlycard: isOnlyCard, web: !isHandCorrent, hand: isHandCorrent, 'is-big-mixin': isMixinBigQues, 'layout-a33': isA33Layout, 'is-small-mixin': isMixinSmallQues }">

    <div v-drag class="edit-button-tools nosave btn-tool">
      <el-input-number class="btn-change-linenum" @change="handleVLinesChange" v-model="item.verticalLines" :max="5"
        :min="2" size="small" v-if="item.showType == 'vertical'" />

      <el-radio-group class="btn-change-coltype" size="small" @change="handleChangeColType" v-model="item.showType"
        v-if="isChoiceQuesType">
        <el-radio-button label="vertical">竖排</el-radio-button>
        <el-radio-button label="horizontal">横排</el-radio-button>
      </el-radio-group>

      <div class="edit click-element" @click="editQues" v-if="showEditTools"></div>
    </div>

    <div class="ques-content" :class="{ 'is-big-mixin': isMixinBigQues, 'is-small-mixin': isMixinSmallQues }"
      ref="qWrapRef">

      <!-- 调整高度工具 -->
      <object-height-tool class="object-height-tools" ref="objectHeightToolRef" :qId="qId" :splitNodes="splitNodes"
        :refreshKey="refreshKey" @setQuesCardHeight="setQuesCardHeight" v-if="!isMixinBigQues"></object-height-tool>

      <!-- 大题标题 -->
      <bigques-name :class="{ 'split-tag': isMixinBigQues }" :item="item" v-if="showBigTitle"></bigques-name>

      <div class="splitnode-container" v-if="!isMixinBigQues">
        <!-- splitNodes {{splitNodes}} -->
        <template v-for="(spItem, spIndex) in splitNodes" :key="spItem.id">
          <div class="ques-box-container" :style="{ width: spItem.width }" v-if="spItem.type === 'ques'">
            <!-- 混合题小问标题 -->
            <div class="ques-content-name--small" contenteditable="true"
              v-if="spIndex === 0 && isMixinSmallQues && questionCard.showName === '1' && !item.hideSmallName">
              <span v-if="item.quesFullName">{{ item.quesFullName }}</span>
              <span v-else>{{ item.quesName }}.({{ item.score }}分)</span>
            </div>

            <!-- 选择题 -->
            <div class="ques-box split-card no-items" :data-index="spIndex" :data-id="spItem.id"
              :style="{ height: spItem.height }" :class="{ vertical: item.showType == 'vertical' }"
              v-if="isChoiceQuesType">
              <template v-for="( group, index ) in  spItem.data || optionList " :key="group.id">
                <!-- inline-flex换行标签，不可删除 -->
                <div v-if="item.showType == 'vertical' && group.alone"></div>

                <!-- 竖排模式以群组为分段 -->
                <div class="ques-item-wrap" :class="{ 'vertical split_tag-group': item.showType == 'vertical' }"
                  :style="{ maxHeight: choiceGroupMaxHeight }">
                  <div class="choice-item split-tag" :data-index="`${index},${subIndex}`"
                    v-for="(subItem, subIndex) in group.list" :key="subItem.id"
                    :class="{ horizontal: item.showType == 'horizontal' }">

                    <!-- 客观题合并的模式下统一在选择题区域展示选择题和判断题 -->
                    <judge-view :index="index" :subItem="subItem" @setAnswer="tapTFButton"
                      v-if="subItem.typeId == QUES_TYPE.judge"></judge-view>
                    <choice-view :index="index" :subItem="subItem" @setAnswer="setAnswer" v-else></choice-view>

                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 分页底部空白区域 -->
          <div class="page-splitor" :data-id="spItem.id" :style="{ height: spItem.height }"
            v-else-if="spItem.type === 'blank'">
          </div>
        </template>
      </div>

    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  render,
  h,
  computed,
  onBeforeUnmount,
} from 'vue';
import Paper from '@/views/paper';
import {
  DECIMAL_TYPE,
  GRIDPLACE_TYPE,
  GRID_TYPE,
  JUDGE_TYPE,
  MARK_TYPE,
  QUES_TYPE,
  IPAGELAYOUT,
  LINE_WIDTH,
  ICorrectType,
  ICARDMODEL,
  QUES_SCAN_MODE
} from '@/typings/card';
import { deepClone, generateUUID, sleep } from '@/utils/util';
import bus from '@/utils/bus';
import { SplitNodes } from '@/typings/card';
import {
  getNameElement,
  getObjectiveMaxHeight,
  getObjectiveMinHeight,
  pxConversionMm,
} from '@/utils/dom';

import MarkScore from '../MarkScore.vue';
import ObjectHeightTool from './ObjectHeightTool.vue';
import BigquesName from './BigquesName.vue';
import ChoiceView from './QuesCardObject/Choice.view.vue';
import JudgeView from './QuesCardObject/Judge.view.vue';
import { PaperConstant } from '@/views/paper.constant';

export default defineComponent({
  props: {
    qId: {
      type: String,
      default: '',
    },
    // 题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 题目下标
    index: {
      type: Number,
      default: 0,
    },
    // 大题目下标
    bigIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ['updateList'],
  components: {
    MarkScore,
    ObjectHeightTool,
    BigquesName,
    ChoiceView,
    JudgeView,
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      uid: generateUUID(),
      isA33Layout: false,

      QUES_TYPE: QUES_TYPE,
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      JUDGE_TYPE: JUDGE_TYPE,
      MARK_TYPE: MARK_TYPE,
      GRID_TYPE: GRID_TYPE,
      DECIMAL_TYPE: DECIMAL_TYPE,
      ICorrectType: ICorrectType,
      LINE_WIDTH: LINE_WIDTH,
      QUES_SCAN_MODE:QUES_SCAN_MODE,
      Paper: Paper,
      isFocus: false,
      isEdit: true,
      wrapHeight: 0,
      showType: props.item.showType || 'vertical',
      isObj: false,
      az: '0ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      height: '' as any,
      // 选择题选项
      optionList: [props.item.data],
      // 是否混合题的小问
      isMixinSmallQues: !!props.item.parentId,
      isOnlyCard: Paper.cardType != ICARDMODEL.QUESCARD,
      isHandCorrent: Paper.correctType == ICorrectType.HAND,
      // 客观题是否合并
      isObjectiveMerged: Paper.mergeQues,
      // 拆分节点，type:'ques'|'blank'
      splitNodes: [{ id: generateUUID(), type: 'ques', height: 'auto' }] as SplitNodes[],
      // 选择题选项总数量
      selectOptionTotal: 0,

      // 强制刷新key
      refreshKey: 0,
    });

    const quesCardRef = ref<HTMLElement | any>(null);
    const objectHeightToolRef = ref<HTMLElement | any>(null);
    const qWrapRef = ref<HTMLElement | any>(null);

    // 是否混合题大题
    const isMixinBigQues = computed(() => {
      return props.item.typeId == QUES_TYPE.mixin && !props.item.parentId;
    });

    // 选择题组最大高度
    const choiceGroupMaxHeight = computed(() => {
      const CHOICE_HEIGHT_PER = 6.2;
      return props.item.showType === 'vertical'
        ? props.item.verticalLines * CHOICE_HEIGHT_PER + 'mm'
        : 'none';
    });

    // 是否选择题型
    const isChoiceQuesType = computed(() => {
      return props.item.typeId == QUES_TYPE.singleChoice || props.item.typeId == QUES_TYPE.choice || props.item.typeId == QUES_TYPE.judge;
    });

    // 块边距
    const blockMargin = computed(() => {
      return isChoiceQuesType.value ? '1.3mm 0' : '';
    });

    const questionCard = computed(() => {
      return props.item;
    });

    // 是否显示大标题
    const showBigTitle = computed(() => {
      let exculdeMixin =
        (state.Paper.mergeQues && state.Paper.isChoiceQues(props.item.typeId)) ||
        !state.isMixinSmallQues;

      return props.item.showName == '1' && props.item.hasTitle && exculdeMixin;
    });

    // 是否显示编辑按钮
    const showEditTools = computed(() => {
      if (isChoiceQuesType.value) return !state.isObjectiveMerged;
      return true;
    });

    /**
     * @name 检查是否A33布局
     */
    const checkIsA33Layout = () => {
      let pageLayout = Number(Paper.pageLayout);
      if ([IPAGELAYOUT.A4, IPAGELAYOUT.A3, IPAGELAYOUT.A33].includes(pageLayout))
        return [IPAGELAYOUT.A33].includes(pageLayout);

      let isA33Card = [IPAGELAYOUT.A33].includes(pageLayout);
      let $cardDom: any = document.getElementById(props.qId);
      if (!$cardDom && isA33Card) return true;

      if ($cardDom.style.width) {
        isA33Card = $cardDom.style.width === PaperConstant.WIDTH_A33;
      } else if ($cardDom.previousElementSibling.style.width) {
        isA33Card = $cardDom.previousElementSibling.style.width === PaperConstant.WIDTH_A33;
      }

      return isA33Card;
    };

    const reCaculQuestionPos = async () => {
      if (props.item.typeId == QUES_TYPE.mixin) return;

      let checkA33 = checkIsA33Layout();
      let a33layoutChange = state.isA33Layout !== checkA33;
      state.isA33Layout = checkA33;
      state.isObjectiveMerged = Paper.mergeQues;
      if (isChoiceQuesType.value) {
        await nextTick();
        renderChoiceOptions(a33layoutChange);
      }
      state.refreshKey++;
    };

    /* 处理切换横竖拍 */
    const handleChangeColType = () => {
      syncSettingToOrigin(props.item.id, props.item?.data[0]?.id, 'showType', props.item.showType);
      renderChoiceOptions(true);
    };

    /* 处理竖向排列行数变化 */
    const handleVLinesChange = () => {
      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      let qItem = Paper.getQuesInfos()[qIndex];
      if (qItem) qItem.verticalLines = props.item.verticalLines;

      syncSettingToOrigin(props.item.id, props.item?.data[0]?.id, 'verticalLines', props.item.verticalLines);
      renderChoiceOptions(true);
    };

    const renderScore = () => {
      if (isMixinBigQues.value) return;

      state.isOnlyCard = Paper.cardType != ICARDMODEL.QUESCARD;
      state.isHandCorrent = Paper.correctType == ICorrectType.HAND;

      if (
        (props.item.typeId == QUES_TYPE.choice || props.item.typeId == QUES_TYPE.singleChoice) &&
        Paper.correctType == ICorrectType.WEB
      )
        return;
      if (props.item.scanMode == QUES_SCAN_MODE.AI_FILL) return;
      // 混合大题跳过计算
      if (isMixinBigQues.value) return;

      const splitEls = document.querySelectorAll(`[qid="${props.item.data[0].id}"]`);
      Array.from(splitEls).forEach((item: any) => {
        item?.getElementsByClassName('score-container')[0]?.remove();

        if (Paper.correctType == ICorrectType.WEB) return;
        if (props.item.scanMode == QUES_SCAN_MODE.AI_FILL) return;
      });
      let el = document.createElement('span');
      el.className = 'score-container noeditsave';

      render(
        h(MarkScore, {
          typeId: props.item.typeId,
          'mark-type': props.item.markType,
          score: Number(props.item.data[0].score),
          step: props.item.step,
          isDecimal: props.item.decimal == DECIMAL_TYPE.HAVE,
          isSplit: props.item.gridType == GRID_TYPE.YES,
          id: props.item.data[0].id,
        }),
        el
      );
    };

    /* 设置题目卡片的总高度 */
    const setQuesCardHeight = () => {
      if (!quesCardRef.value) return;

      let offsetHeight = quesCardRef.value.offsetHeight;
      let splitorDoms = quesCardRef.value.querySelectorAll('.page-splitor');
      let splitorDomsHeight = 0;
      if (splitorDoms.length) {
        Array.from(splitorDoms).forEach(($dom: HTMLElement) => {
          splitorDomsHeight += $dom.offsetHeight;
        });
      }
      let tHeight = pxConversionMm(offsetHeight - splitorDomsHeight);

      props.item.height = tHeight + 'mm';
      state.height = tHeight;
      syncSettingToOrigin(props.item.id, props.item?.data[0]?.id, 'height', props.item.height);

      return tHeight;
    };

    /* 检查分段高度是否合适 */
    const checkSplitNodeHeight = () => {
      // 检测是否需要重新刷新高度
      let needRefresh = false;
      state.splitNodes.forEach((sNode, index) => {
        if (index === 0 || sNode.type === 'blank') return;

        let domBoxs = document.querySelectorAll(`[data-id="${sNode.id}"] .ques-item-wrap`);
        let oldHeight = Number(sNode.height.replace('mm', ''));
        // let newHeight = pxConversionMm($dragDomBox.offsetHeight);
        let newHeight = pxConversionMm(getObjectiveMinHeight(Array.from(domBoxs)));
        needRefresh = newHeight > oldHeight;
        if (!needRefresh) return;

        // 大于0的分段计算最小高度
        sNode.height = newHeight + 'mm';
      });
    };

    /* 处理拆分卡片的消息 - 跨页拆分 */
    const handlePageSplitCard = async ({
      id,
      splitNodes,
    }: {
      id: string;
      splitNodes: SplitNodes[];
    }) => {
      if (props.qId !== id || isMixinBigQues.value) return;

      if (isChoiceQuesType.value) {
        // 选择题
        let tagIndex = splitNodes[0].tagIndex.split(',');
        let groupIndex = Number(tagIndex[0]),
          subIndex = Number(tagIndex[1]);

        state.splitNodes = splitNodes.map((sNode, sIndex) => {
          if (sNode.type !== 'ques') return sNode;

          let data = [];
          // 选择题按分组的方式拆分题目
          if (sIndex === 0) {
            // 首个分段
            for (let index = 0; index < state.optionList.length; index++) {
              const group = state.optionList[index];
              if (index < groupIndex) {
                data.push(group);
                continue;
              }

              // 遍历获取组中的元素
              let lastGroup = deepClone(group);
              lastGroup.list = group.list.slice(0, subIndex);

              data.push(lastGroup);
              break;
            }
          } else {
            // 其他分段
            for (let index = 0; index < state.optionList.length; index++) {
              const group = state.optionList[index];
              if (index < groupIndex) continue;

              if (index === groupIndex) {
                // 遍历获取组中的元素
                let lastGroup = deepClone(group);
                lastGroup.list = group.list.slice(subIndex, group.list.length);
                data.unshift(lastGroup);
              } else {
                data.push(group);
              }
            }
          }

          return {
            ...sNode,
            data,
          };
        });
      } else {
        state.splitNodes = splitNodes.map((sNode, sIndex) => {
          if (sNode.type !== 'ques') return sNode;

          let data = [];
          // 判断题
          let originData = props.item.data[0].data || props.item.data;
          let tagIndex = splitNodes[0].tagIndex;

          if (sIndex > 0) {
            data = originData.slice(tagIndex, originData.length);
          } else {
            data = originData.slice(0, tagIndex);
          }

          return {
            ...sNode,
            data,
          };
        });
      }
      state.refreshKey++;

      await nextTick();
      checkSplitNodeHeight();
    };

    /* 获取跨页拆分的高度和 */
    const getSplitSumQuesH = (tHeight?: number) => {
      tHeight = tHeight || setQuesCardHeight();
      let $dom = quesCardRef.value;
      // 减去分数和标题栏高度
      let $title = getNameElement($dom);
      if ($title) {
        tHeight -= pxConversionMm($title.offsetHeight);
      }

      return tHeight;
    };

    /* 合并所有拆分的卡片 - 跨页合并 */
    const mergeAllPageSplitedCards = async (id?: string) => {
      if (
        (id !== null && props.qId !== id) ||
        isMixinBigQues.value ||
        state.splitNodes.length === 1
      )
        return;

      let firstNode = deepClone(state.splitNodes[0]);
      firstNode.height = getSplitSumQuesH() + 'mm';
      firstNode.data = [];

      if (isChoiceQuesType.value) {
        // 选择题
        firstNode.data = state.optionList;
      } else {
        // 判断题
        state.splitNodes.forEach(item => {
          if (!item.data) return;

          firstNode.data = firstNode.data.concat(item.data);
        });
      }

      state.splitNodes = [firstNode];
      state.refreshKey++;

      await nextTick();
      setQuesCardHeight();
    };

    const handleCardRendered = async (id: string) => {
      if (props.qId !== id || !isChoiceQuesType.value) return;

      let isA33Layout = checkIsA33Layout();
      if (state.isA33Layout == isA33Layout) return;

      state.isA33Layout = isA33Layout;
      await nextTick();
      if (state.splitNodes.length) {
        if (state.splitNodes.length === 1) {
          let domBoxs = $(`[data-id="${state.splitNodes[0].id}"] .ques-item-wrap`);
          let boxheight = pxConversionMm(getObjectiveMaxHeight(Array.from(domBoxs)));

          state.splitNodes[0].height = boxheight + 'mm';
        }
        await nextTick();
        setQuesCardHeight();
      }
    }

    const handleReachBottom = async ({ id, pageSize }: { id: string; pageSize: number }) => {
      if (props.qId !== id) return;
      await nextTick();

      if (objectHeightToolRef.value) objectHeightToolRef.value.setHeightFull();
      // console.log('reachBottom', id);
    };

    const handleSwitchMergeQues = async () => {
      if (state.isObjectiveMerged == Paper.mergeQues) return;

      state.isObjectiveMerged = Paper.mergeQues;
      state.refreshKey++;
      if (isChoiceQuesType.value) {
        await nextTick();
        renderChoiceOptions(true);
      }
    };

    onBeforeUnmount(() => {
      bus.off('cardRendered', handleCardRendered);
      bus.off('splitPageCard', handlePageSplitCard);
      bus.off('mergePageSplitedCard', mergeAllPageSplitedCards);
      bus.off('reachBottom', handleReachBottom);
      bus.off('switchCorrect', renderScore);

      // Paper.off('switchMergeQues', handleSwitchMergeQues);
      Paper.off('changeLayout', reCaculQuestionPos);
      Paper.off('changeCardType', reCaculQuestionPos);
      Paper.off('updateAllQustions', reCaculQuestionPos);
      Paper.off('changeNumberLayout', reCaculQuestionPos);
    });

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      bus.on('cardRendered', handleCardRendered);
      bus.on('reachBottom', handleReachBottom);
      bus.on('splitPageCard', handlePageSplitCard);
      bus.on('mergePageSplitedCard', mergeAllPageSplitedCards);
      bus.on('switchCorrect', renderScore);

      // Paper.on('switchMergeQues', handleSwitchMergeQues);
      Paper.on('changeLayout', reCaculQuestionPos);
      Paper.on('changeCardType', reCaculQuestionPos);
      Paper.on('updateAllQustions', reCaculQuestionPos);
      Paper.on('changeNumberLayout', reCaculQuestionPos);

      state.isA33Layout = checkIsA33Layout();
      let quesHeight = props.item.height;
      if (quesHeight) {
        state.height = quesHeight.replace('mm', '');
        let height = getSplitSumQuesH(state.height);
        if (height < 8) {
          // 保存的分段高度过低，自动计算合适高度
          checkSplitNodeHeight();
        } else {
          state.splitNodes[0].height = getSplitSumQuesH(state.height) + 'mm';
        }
      }
      state.refreshKey++;

      if (isChoiceQuesType.value) {
        await nextTick();
        await renderChoiceOptions();
        console.debug("card height:", state.splitNodes[0].height)
      }
    });

    const editQues = () => {
      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      let qItem = Paper.getQuesInfos()[qIndex];

      bus.emit('editQues', { item: deepClone(qItem), index: qIndex });
    };

    /* 拆分题目 */
    const splitSubjectiveQuestions = () => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;

      if (!props.item.mixinMode) qId = ques.id;

      Paper.splitSubjectiveQuestions(qId);
      Paper.notifyUpdateData('right');

      setTimeout(function () {
        bus.emit('renderAllCard');
      }, 10);
    };

    /* 合并主观题 */
    const mergeSubjectiveQuestions = async (subIndex?: number) => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;
      if (!props.item.mixinMode) qId = ques.id;

      Paper.mergeSubjectiveQuestions(qId, subIndex);
      Paper.notifyUpdateData('right');

      setTimeout(function () {
        bus.emit('renderAllCard');
      }, 10);
    };

    /* 改变竖直排列的最大行数 */
    const changeVerticalLineNum = (val: number) => {
      console.log('changeVerticalLineNum', val);
    };

    /**
     * @name 分组选择题
     * @description 按选择的选项不同来分组，小于4个选项为统统为一组，超过的不同选项分别立组
     */
    const renderChoiceOptions = async (renderAll: boolean = false) => {
      if (!isChoiceQuesType.value) return;

      checkDataComplete();
      if (Paper.mergeQues == 1) {
        // 合并题型时在首个位置标记showType
        let firstBigChoice = Paper.findBigQuesByTypeId([QUES_TYPE.singleChoice, QUES_TYPE.choice]);
        if (firstBigChoice) firstBigChoice.showType = props.item.showType;
      }

      let optionList = [];
      class OptionGroup {
        id = generateUUID();
        list = [];
        // 选项数量
        optionCount = 4;
        // 是否独立成组
        alone = false;
        // 行数
        lines = props.item.verticalLines;

        get isFull() {
          // 是否达到一组上限
          return this.list.length === this.lines;
        }

        constructor(optionCount: number = 4, alone: boolean = false) {
          this.optionCount = optionCount;
          this.alone = alone;
        }

        addItem(item: any) {
          if (item.optionCount === undefined) item.optionCount = 4;
          if (item.answer === undefined) item.answer = '';
          this.list.push(item);
        }
      }

      let group = new OptionGroup();
      if (props.item.showType == 'vertical') {
        // 竖排模式
        // 标准选线数量
        let standartCount = 4;
        props.item.data.forEach((subItem: any, index: number) => {
          if (index === 0) group.optionCount = subItem.optionCount;

          if (index === props.item.data.length - 1) {
            group.addItem(subItem);
            optionList.push(group);
            return;
          }

          if (index > 0 && subItem.groupAlone === '1') {
            optionList.push(group);

            group = new OptionGroup(subItem.optionCount, true);
            group.addItem(subItem);
          } else {
            if (subItem.optionCount === group.optionCount) {
              group.addItem(subItem);
            } else {
              if (group.optionCount <= standartCount && subItem.optionCount <= standartCount) {
                // 小于标准的收集到一起
                group.addItem(subItem);
              } else {
                optionList.push(group);

                group = new OptionGroup(subItem.optionCount);
                group.addItem(subItem);
              }
            }
          }

          if (group.isFull) {
            optionList.push(group);
            group = new OptionGroup(subItem.optionCount);
          }
        });
      } else {
        // 横排模式
        props.item.data.forEach((subItem: any, index: number) => {
          if (index === props.item.data.length - 1) {
            group.addItem(subItem);
            optionList.push(group);
            return;
          }

          if (index > 0 && subItem.groupAlone === '1') {
            optionList.push(group);

            group = new OptionGroup(subItem.optionCount, true);
            group.addItem(subItem);
          } else {
            group.addItem(subItem);
          }
        });
      }

      state.optionList = optionList;
      if (state.splitNodes.length === 1) {
        state.splitNodes[0].data = optionList;
      }

      let optionTotal = 0;
      optionList.forEach(item => (optionTotal += item.list.length));
      let hasCountChanged = state.selectOptionTotal && optionTotal !== state.selectOptionTotal;

      if (renderAll || hasCountChanged) {
        state.selectOptionTotal = optionTotal;
        await nextTick();
        renderAll && bus.emit('renderAllCard');

        await nextTick();
        if (state.splitNodes.length) {
          if (state.splitNodes.length === 1) {
            let domBoxs = $(`[data-id="${state.splitNodes[0].id}"] .ques-item-wrap`);
            let boxheight = pxConversionMm(getObjectiveMaxHeight(Array.from(domBoxs)));

            state.splitNodes[0].height = boxheight + 'mm';
          }
          await nextTick();
          setQuesCardHeight();
        }
      }
    };

    /* 同步设置到源数据 */
    const syncSettingToOrigin = (qId: string, sqId: string, key: string, value: any) => {
      // if (Paper.cardType == ICARDMODEL.BLANKCARD && Paper.mergeQues == 0) return
      //增加保底策略，如果大题id和小题id查找不到对应源数据，则通过大题id查找小题源数据
      let smallQues:any = Paper.findSmallQuesByQuesId(qId)
        || Paper.findBigQuesByQuesId(qId)
        || Paper.findBigQuesBySmallQuesId(sqId)
        || Paper.findSmallQuesByQuesId(sqId)

      if (Paper.cardType==ICARDMODEL.ONLYCARD &&smallQues) {
        let isMixin = Paper.checkQuesIsMixin(smallQues);
        if(isMixin) smallQues = Paper.findSmallQuesByQuesId(sqId)
      }
      if (!smallQues) return

      smallQues[key] = value;
    };

    /**
     * @name 设置选择题答案
     * @param item :当前题
     * @param index:选项
     */
    const setAnswer = (item: any, index: any) => {
      if (item.typeId == QUES_TYPE.choice) {
        // 多选题
        item.answer = item.answer ? item.answer.split(',') : [];
        if (item.answer.includes(state.az[index])) {
          item.answer = item.answer.filter((ite: any) => {
            return ite != state.az[index];
          });
        } else {
          item.answer.push(state.az[index]);
        }
        item.answer = item.answer.join(',');
      } else {
        // 单选题
        item.answer = state.az[index];
      }

      // TIP: 这里需要手动同步客观题答案，兼容题卡合一混合题导致数据隔离无法同步的情况
      syncSettingToOrigin(item.id, item.id, 'answer', item.answer);

      const data = {
        from: Paper.abType,
        type: 'updateQuestions',
        func: 'changeAnswer',
        arguments: [item],
      }
      window.parent.postMessage(JSON.stringify(data), '*');

      Paper.notifyUpdateData('card');
    };

    /* 点击判断题按钮 */
    const tapTFButton = (item: any, answer: string) => {
      item.answer = answer;
      // TIP: 这里需要手动同步客观题答案，兼容题卡合一混合题导致数据隔离无法同步的情况
      syncSettingToOrigin(item.id, item.id, 'answer', item.answer);

      const data = {
        from: Paper.abType,
        type: 'updateQuestions',
        func: 'changeAnswer',
        arguments: [item],
      }
      window.parent.postMessage(JSON.stringify(data), '*');

      Paper.notifyUpdateData('card');
    };

    /* 检查数据完整性 */
    const checkDataComplete = () => {
      let data = props.item;

      if (isChoiceQuesType.value) {
        // 选择题检查是否有showType和verticalLines
        if (data.showType === undefined) props.item.showType = 'vertical';
        if (data.verticalLines === undefined) props.item.verticalLines = 5;
      }
      if (props.item.typeId == QUES_TYPE.judge) {
        // 判断题，补全judgeType属性
        props.item.data.forEach(subItem => {
          if (subItem.judgeType === undefined) subItem.judgeType = props.item.judgeType;
        });
      }
    };

    checkDataComplete();

    return {
      ...toRefs(state),
      qWrapRef,
      quesCardRef,
      objectHeightToolRef,
      isChoiceQuesType,
      isMixinBigQues,
      choiceGroupMaxHeight,
      blockMargin,
      questionCard,
      showEditTools,
      showBigTitle,
      editQues,
      mergeSubjectiveQuestions,
      splitSubjectiveQuestions,
      renderChoiceOptions,
      handleVLinesChange,
      handleChangeColType,
      changeVerticalLineNum,
      setAnswer,
      tapTFButton,
      setQuesCardHeight,
    };
  },
});
</script>

<style lang="scss" scoped>
.q-opt {
  &:hover .edit-button-tools {
    display: block;
  }
}

.opttion-txt--judge.iconfont {
  font-size: 2.6mm;
  position: relative;
  top: 0.35mm;
  transform: scale(1.2);
  display: inline-block;
}

.q-opt:hover .pop.down {
  display: block;
}

::v-deep .ques-sort {
  cursor: text;

  &:hover,
  &:focus {
    background-color: #eee;
  }
}

.choice-item {
  margin-bottom: 1mm;
  display: inline-block;
}

.choice-item,
.choice-item .ques-item {
  font-family: 'Times New Roman';
}
</style>
