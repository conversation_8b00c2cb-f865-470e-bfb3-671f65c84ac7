/*
 * @Description: DOM操作工具类 - 统一处理DOM创建和操作逻辑
 * @Author: 优化重构
 * @Date: 2024-12-19
 */

import { render, h } from 'vue';
import { IPAGELAYOUT, INUMBERLAYOUT } from '@/typings/card';
import { LayoutUtils } from './layout';
import { footerKeepHeight, getHeaderInfoHeight } from './config';
import bus from '@/utils/bus';

// 组件导入
import PageHeader from '@/components/PageHeader.vue';
import PageHeaderInfo from '@/components/PageHeaderInfo.vue';
import PageHeaderInfoA33 from '@/components/PageHeaderInfoA33.vue';
import PageHeaderInfoWrite from '@/components/PageHeaderInfoWrite.vue';
import PageHeaderInfoQRCode from '@/components/PageHeaderInfoQRCode.vue';
import PageHeaderInfoMoreNo from '@/components/PageHeaderInfoMoreNo.vue';
import PageFooter from '@/components/PageFooter.vue';
import HandWriteArea from '@/components/HandWriteArea.vue';

/**
 * 页面元素创建上下文
 */
export interface IPageElementContext {
  layout: IPAGELAYOUT;
  numberLayout: INUMBERLAYOUT;
  pageNum: number;
}

/**
 * DOM操作工具类
 */
export class DOMOperations {
  /**
   * 获取定位点状态
   * @param layout 页面布局
   * @param page 当前页码
   * @returns 定位点位置状态
   */
  static getPosPointState(layout: IPAGELAYOUT, page: number): { left: boolean; right: boolean } {
    const step = LayoutUtils.getLayoutStep(layout);
    let position = { left: true, right: true };

    switch (layout) {
      case IPAGELAYOUT.A4_TWO_COLUMN:
        // A4两栏布局，按照A3的逻辑处理定位点
        if (page % step === 1 || page % step === 3) {
          position = { left: true, right: false };
        } else if (page % step === 2 || page % step === 0) {
          position = { left: false, right: true };
        }
        break;

      case IPAGELAYOUT.A3:
        if (page % step === 1 || page % step === 3) {
          position = { left: true, right: false };
        } else if (page % step === 2 || page % step === 0) {
          position = { left: false, right: true };
        }
        break;

      case IPAGELAYOUT.A33:
        if (page % step === 1 || page % step === 4) {
          position = { left: true, right: false };
        } else if (page % step === 3 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
        break;

      case IPAGELAYOUT.A32:
        if (page % step === 1 || page % step === 4) {
          position = { left: true, right: false };
        } else if (page % step === 3 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
        break;

      case IPAGELAYOUT.A23:
        if (page % step === 1 || page % step === 3) {
          position = { left: true, right: false };
        } else if (page % step === 2 || page % step === 0) {
          position = { left: false, right: true };
        } else {
          position = { left: false, right: false };
        }
        break;

      default:
        // A4 布局保持默认
        break;
    }

    return position;
  }

  /**
   * 创建页头元素
   * @param context 页面元素上下文
   * @returns 页头DOM元素
   */
  static createHeader(context: IPageElementContext): HTMLElement {
    const { layout, pageNum } = context;
    const headerDiv = document.createElement('div');
    headerDiv.style.height = footerKeepHeight + 'mm';
    headerDiv.className = 'custom-div header-box';

    render(
      h(PageHeader, {
        position: this.getPosPointState(layout, pageNum),
      }),
      headerDiv
    );

    return headerDiv;
  }

  /**
   * 创建页头信息元素
   * @param context 页面元素上下文
   * @returns 页头信息DOM元素
   */
  static createHeaderInfo(context: IPageElementContext): HTMLElement {
    const { layout, numberLayout, pageNum } = context;
    const headerInfoDiv = document.createElement('div');
    headerInfoDiv.style.height = getHeaderInfoHeight() + 'mm';
    headerInfoDiv.className = 'custom-div';

    // 根据考号类型选择对应组件
    let headerInfoComp;
    switch (numberLayout) {
      case INUMBERLAYOUT.WRITE:
        headerInfoComp = PageHeaderInfoWrite;
        break;
      case INUMBERLAYOUT.QRCODE:
        headerInfoComp = PageHeaderInfoQRCode;
        break;
      case INUMBERLAYOUT.MORE:
        headerInfoComp = PageHeaderInfoMoreNo;
        break;
      default:
        headerInfoComp = (layout === IPAGELAYOUT.A33 || layout === IPAGELAYOUT.A32) 
          ? PageHeaderInfoA33 
          : PageHeaderInfo;
        break;
    }

    // 计算页码
    const calculatedPage = this.calculateHeaderPage(layout, pageNum);

    render(
      h(headerInfoComp, { page: calculatedPage }),
      headerInfoDiv
    );

    return headerInfoDiv;
  }

  /**
   * 计算页头显示的页码
   * @param layout 页面布局
   * @param pageNum 当前页码
   * @returns 计算后的页码
   */
  private static calculateHeaderPage(layout: IPAGELAYOUT, pageNum: number): number {
    switch (layout) {
      case IPAGELAYOUT.A4:
        return pageNum;
      case IPAGELAYOUT.A4_TWO_COLUMN:
        return Math.ceil(pageNum / 2);
      case IPAGELAYOUT.A3:
        return Math.ceil(pageNum / 2);
      case IPAGELAYOUT.A33:
        return Math.ceil(pageNum / 3);
      default:
        return Math.ceil(pageNum / 2.5);
    }
  }

  /**
   * 创建页脚元素
   * @param context 页面元素上下文
   * @param totalPage 总页数
   * @returns 页脚DOM元素
   */
  static createFooter(context: IPageElementContext, totalPage: number): HTMLElement {
    const { layout, pageNum } = context;
    const layoutConfig = LayoutUtils.getLayoutConfig(layout);
    
    if (!layoutConfig) {
      throw new Error(`Unsupported layout: ${layout}`);
    }

    const footer = document.createElement('div');
    footer.className = 'custom-div footer';
    footer.style.top = `${297 * pageNum - footerKeepHeight}mm`; // 使用常量替代pageHeight

    // 计算页面标签和实际页码
    const pageText = LayoutUtils.getPageText(layout, pageNum);
    const actualPage = LayoutUtils.calculateActualPage(layout, pageNum);

    render(
      h(PageFooter, {
        page: actualPage,
        totalPage,
        pageText,
        position: this.getPosPointState(layout, pageNum),
      }),
      footer
    );

    return footer;
  }

  /**
   * 创建手写区域元素
   * @returns 手写区域DOM元素
   */
  static createHandWriteArea(): HTMLElement {
    const handWriteDiv = document.createElement('div');
    handWriteDiv.style.position = 'absolute';
    handWriteDiv.style.left = '-14mm';
    handWriteDiv.className = 'custom-div hand-write-div';

    render(h(HandWriteArea, {}), handWriteDiv);
    return handWriteDiv;
  }

  /**
   * 移除自定义插入的DOM节点
   */
  static removeCustomDivs(): void {
    // 移除custom-div元素
    const customDivs = document.body.getElementsByClassName('custom-div');
    for (let i = customDivs.length - 1; i >= 0; i--) {
      if (customDivs[i] != null) {
        customDivs[i].parentNode?.removeChild(customDivs[i]);
      }
    }

    // 移除split-ques元素
    const splitQues = document.body.getElementsByClassName('split-ques');
    for (let i = splitQues.length - 1; i >= 0; i--) {
      if (splitQues[i] != null) {
        const element = splitQues[i] as HTMLElement;
        bus.emit('update' + element.id);
        
        // 销毁跨页生成的编辑器
        const editorId = `ck_${element.id}_cp${element.getAttribute('page')}`;
        const editor = (window as any).CKEDITOR?.instances?.[editorId];
        if (editor) {
          editor.destroy();
        }
        
        element.parentNode?.removeChild(element);
      }
    }

    // 清理数学公式相关的script标签
    Array.from(document.getElementsByClassName('ck-math-tex')).forEach(el => {
      const scriptTag = el.getElementsByTagName('script')[0];
      if (scriptTag) {
        scriptTag.parentNode?.removeChild(scriptTag);
      }
    });
  }

  /**
   * 替换其他类名（用于DOM合并操作）
   * @param str 原始类名字符串
   * @returns 处理后的类名字符串
   */
  static replaceOtherClass(str: string): string {
    return str.replace(/ |drag|over/g, '');
  }

  /**
   * 批量设置元素样式
   * @param element DOM元素
   * @param styles 样式对象
   */
  static setElementStyles(element: HTMLElement, styles: Record<string, string>): void {
    Object.entries(styles).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
  }

  /**
   * 安全地移除元素
   * @param element 要移除的元素
   */
  static safeRemoveElement(element: HTMLElement): void {
    if (element && element.parentNode) {
      element.parentNode.removeChild(element);
    }
  }

  /**
   * 检查元素是否包含指定类名
   * @param element DOM元素
   * @param className 类名
   * @returns 是否包含类名
   */
  static hasClass(element: HTMLElement, className: string): boolean {
    return element.classList.contains(className);
  }

  /**
   * 安全地添加类名
   * @param element DOM元素
   * @param className 类名
   */
  static addClass(element: HTMLElement, className: string): void {
    if (!this.hasClass(element, className)) {
      element.classList.add(className);
    }
  }

  /**
   * 安全地移除类名
   * @param element DOM元素
   * @param className 类名
   */
  static removeClass(element: HTMLElement, className: string): void {
    if (this.hasClass(element, className)) {
      element.classList.remove(className);
    }
  }
}
