<template>
  <div class="page-box page-box--ques quescard noselect" :style='{
    "--fontSize": data.fontSize,
    "--fontFamily": data.fontFamily,
    "--lineHeight": data.space,
    width: data.pageLayout == IPAGELAYOUT.A33
      ? "130mm"
      : "192mm",
  }'>
    <div class="header-box" :style='{ height: footerKeepHeight + "mm" }'>
      <page-header :position="getPosPointState(1)"></page-header>
    </div>
    <div class="hand-write-div" :style='{ "position": "absolute", "left": "-14mm" }'>
      <hand-write-area></hand-write-area>
    </div>
    <div :style='{ height: headerHeight + "mm", zIndex: 9 }'>
      <template v-if="data.numberLayout == INUMBERLAYOUT.WRITE">
        <page-header-info-write :page="1"></page-header-info-write>
      </template>
      <template v-else-if="data.numberLayout == INUMBERLAYOUT.QRCODE">
        <page-header-info-q-r-code :page="1"></page-header-info-q-r-code>
      </template>
      <template v-else-if="data.numberLayout == INUMBERLAYOUT.MORE">
        <page-header-info-more-no :page="1"></page-header-info-more-no>
      </template>
      <template v-else>
        <page-header-info v-if="data.pageLayout != IPAGELAYOUT.A33 && data.pageLayout != IPAGELAYOUT.A32"
          :page="1"></page-header-info>
        <page-header-info-a-33 v-else :page="1"></page-header-info-a-33>
      </template>
    </div>
    <template v-for="(item, index) in quesInfos"
      v-memo="[item.key,item.data,item.hideQuesSurface,data.ansToTopConfig.object,data.ansToTopConfig.fill]">
      <div v-if="item.showName == 1 && item.typeId != QUES_TYPE.noAnswerArea" class="ques-name-box"
        :style='{ "minHeight": data.space, "lineHeight": data.space, "clear": "both", "overflow": "hidden", "position": "relative" }'
        contenteditable='true' v-html="item.htmlname || item.name" @blur="((e) => { changeName(e, item) })"
        :id="'name_' + item.id" :key="item.id"></div>
      <template v-if="item?.doQuesList?.length">
        <div>请将所选题目对应的题号涂黑,并在对应题目区域作答,如果多涂,则按所涂的第一题计分。</div>
        <div v-for="doQues in item?.doQuesList" :id="'choose_' + doQues.ids.join('_')">
          <span>我选做的题目是（{{ doQues.ids.length }}选{{ doQues.doCount }}）</span>
          <span v-for="(name, ni) in doQues.name" :id="'choose_' + doQues.ids[ni]"
            style="line-height: 3mm;margin: 0 2mm;">[<span style="padding: 0px 1mm;margin: 0px;font-size: 3mm;">{{ name
            }}</span>]</span>
        </div>
      </template>
      <template v-if="(data.ansToTopConfig.fill && (QUES_TYPE.fill == item.typeId ||
        QUES_TYPE.fillEva == item.typeId)) || (data.ansToTopConfig.object && (QUES_TYPE.singleChoice == item.typeId ||
          QUES_TYPE.choice == item.typeId ||
          QUES_TYPE.judge == item.typeId))">
        <ques-card-fill v-if="QUES_TYPE.fill == item.typeId || QUES_TYPE.fillEva == item.typeId"
          :key="'QuesCardFill_' + item.id" :qId="item.id" :item="item" :index="index" :bigIndex="index"
          :hasTitle="item.hasTitle = false"></ques-card-fill>
        <empty-ques v-else :key="'EmptyQues_' + item.id" :qId="item.id" :item="item" :index="index" :bigIndex="index"
          :hasTitle="item.hasTitle = false"></empty-ques>
      </template>
      <no-answer-area v-if="item.typeId == QUES_TYPE.noAnswerArea" :key="'NoAnswerArea_' + item.id" :qId="item.id"
        :item="item" :index="index" :bigIndex="index" :hasTitle="item.hasTitle = false"></no-answer-area>
      <template v-else>
        <template v-for="(sitem, sindex) in item.data" :key="sitem.id">
          <ques-item :item="sitem" :info="sitem" :index="index" :style="{
            'display': isHideQues(item, data, sindex) ? 'none' : 'block'
          }"></ques-item>
          <img-merge-list :data="data.groupImgData" :qid="sitem.id"></img-merge-list>
        </template>
      </template>

    </template>

  </div>
</template>
<script>
import PageHeader from '@/components/PageHeader.vue';
import PageHeaderInfo from '@/components/PageHeaderInfo.vue';
import PageHeaderInfoA33 from '@/components/PageHeaderInfoA33.vue';
import PageHeaderInfoWrite from '@/components/PageHeaderInfoWrite.vue';
import PageHeaderInfoQRCode from '@/components/PageHeaderInfoQRCode.vue';
import PageHeaderInfoMoreNo from '@/components/PageHeaderInfoMoreNo.vue'
import HandWriteArea from '@/components/HandWriteArea.vue';
import PageFooter from '@/components/PageFooter.vue';
import QuesItem from '@/components/QuesItem.vue';
import EmptyQues from '@/components/empty/EmptyQues.vue';
import NoAnswerArea from '@/components/empty/NoAnswerArea.vue';
import QuesCardSubject from '@/components/empty/QuesCardSubject.vue';
import QuesCardFill from '@/components/empty/QuesCardFill.vue';
import ImgMergeList from '@/components/ImgMergeList.vue';
import {
  render,
  defineComponent,
  h,
  onMounted,
  onBeforeUnmount,
  onBeforeMount,
  reactive,
  watch,
  nextTick,
  computed,
  toRefs,
} from 'vue';
import { ICARDMODEL, ICorrectType, IPAGELAYOUT, INUMBERLAYOUT } from '@/typings/card';
import { QUES_TYPE } from '@/typings/card';
import { arabToChinese } from '@/utils/util';
import bus from '@/utils/bus';
import { ElLoading } from 'element-plus';
import Paper from './paper';
import { getHeaderInfoHeight } from '@/utils/config';

// 导入新的工具类
import { DOMOperations } from '@/utils/domOperations';
import { RenderManager } from '@/utils/renderManager';

export default defineComponent({
  components: {
    PageHeader,
    PageHeaderInfo,
    PageHeaderInfoA33,
    PageHeaderInfoWrite,
    PageHeaderInfoQRCode,
    PageHeaderInfoMoreNo,
    PageFooter,
    QuesCardFill,
    EmptyQues,
    QuesItem,
    QuesCardSubject,
    NoAnswerArea,
    HandWriteArea,
    ImgMergeList
  },
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  setup(props) {
    const state = reactive({
      quesInfos: [],
      // 强制刷新key
      refreshKey: 0,
      isExecu: false,
      groupImgData: Paper.groupImgData
    });
    // 表头高度
    const headerHeight = computed(() => {
      //定义变量 以便计算属性可以进行监听
      let isSealLine = props.data.isSealLine;
      let numberLayout = props.data.numberLayout;
      let pageLayout = props.data.pageLayout;
      return getHeaderInfoHeight();
    });
    watch([() => props.data.space, () => props.data.fontSize], async () => {
      reloadCard(false);
    });
    watch([() => props.data.isSealLine], async () => {
      if (props.data.numberLayout == INUMBERLAYOUT.QRCODE) {
        reloadCard(false);
      }
    });
    watch([() => props.data.pageType], async () => {
      if (props.data.pageType) {
        mergeImg();
      } else {
        splitImg();
      }
    });

    const isHideQues = (item, data, index) => {
      return (([QUES_TYPE.choice, QUES_TYPE.judge, QUES_TYPE.singleChoice].includes(Number(item.typeId)) && data.ansToTopConfig.object) ||
        ([QUES_TYPE.fill, QUES_TYPE.fillEva].includes(Number(item.typeId)) && data.ansToTopConfig.fill)) && (item.hideQuesSurface == '1' || (item.hideSmallQues == '1' && index != 0));
    };

    const isMergeImgType = (typeId) => {
      return typeId == QUES_TYPE.choice || typeId == QUES_TYPE.judge || typeId == QUES_TYPE.singleChoice || typeId == QUES_TYPE.fill;
    };

    const mergeImg = () => {
      props.data.groupImgData.clear();
      state.quesInfos.forEach(item => {
        if (isMergeImgType(item.typeId)) {
          let qid = "";
          item.data.forEach(sitem => {
            if (isMergeImgType(sitem.typeId)) {
              const eles = document.querySelectorAll(`[id="${sitem.id}"]`);
              eles.forEach(ele => {
                // const imgList = ele.querySelectorAll('img');
                const imgList = [...ele.querySelectorAll('img')]
                  .filter(img => {
                    return (img.width > 60 && img.height > 60) && !$(img).parents('.q-r-o-item').length;
                  });

                imgList.forEach((img, index) => {
                  const scale = img.height / 100;
                  const imgObj = {
                    src: img.src,
                    width: img.width / scale,
                    height: 100,
                    quesId: sitem.id,
                    quesName: sitem.quesNos,
                    sort: imgList.length > 1 ? (index + 1) : ''
                  }
                  $(img).remove();
                  // img.style.display = "none";
                  sitem.isMergeImg = true;
                  if (qid == "") {
                    qid = sitem.id;
                    props.data.groupImgData.set(qid, { h: 100, list: new Array(imgObj) });
                  } else {
                    let width = props.data.groupImgData.get(qid).list.reduce((pre, cur) => {
                      return pre + Number(cur.width);
                    }, 0)
                    width = pxConversionMm(width + imgObj.width);
                    const maxWidth = props.data.pageLayout == IPAGELAYOUT.A33 ? 120 : 180;
                    if (width < maxWidth) {
                      props.data.groupImgData.get(qid).list.push(imgObj);
                    } else {
                      qid = sitem.id;
                      props.data.groupImgData.set(qid, { h: 100, list: new Array(imgObj) });
                    }
                  }
                })
              });
            }
          })
        }
      })
      reloadCard(false);
    }

    const splitImg = () => {
      Array.from(props.data.groupImgData).forEach((group) => {
        group[1].list.forEach(img => {
          let imgEle = document.createElement('img');
          imgEle.src = img.src;
          imgEle.width = img.width;
          imgEle.height = img.height;
          let els = document.querySelectorAll(`[id="${img.quesId}"]`);
          //还原原题图片
          let content = $('.tmpcontent,.hascontent,.ques-content', els[0]);
          if (!content.length) {
            content = $('.tmpcontent,.hascontent,.ques-content', els[1]);
          }
          try {
            content[content.length - 1].append(imgEle);
          } catch (e) {
            content = $('.ques-box', els[0]).append(imgEle);
          }
          let ques = Paper.findSmallQuesByQuesId(img.quesId);
          ques.isMergeImg = false;
        })
      })
      Paper.notifyUpdateData('right');
      props.data.groupImgData.clear();
      reloadCard(false);
    }

    const handleReRenderMathJax = async () => {
      //重置高度，避免切换布局模式高度重叠
      mathjaxLoad(async () => {
        await nextTick();
        renderCard();
        renderOptions();
      })
    };
    const reloadCard = async (isReset = false) => {
      mergeDom();
      removeDom();
      state.quesInfos = Paper.quesInfosInCard;
      await nextTick();
      Paper.trigger('changeLayout', isReset ? 0 : Paper.pageLayout);
      setTimeout(() => {
        mathjaxLoad(async () => {
          renderCard();
          renderOptions();
        });
      }, 500);
    };

    const handleChangeNumberLayout = () => {
      // 更改考号类型，手动触发刷新
      state.refreshKey++;
    };

    const removeStyle = () => {
      let el = document.body.getElementsByClassName('page-box')[0];
      for (let i = 0; i < el.children.length; i++) {
        if (el.children[i].className.indexOf('hand-write-div') >= 0) {
          continue;
        }
        el.children[i].style.width = '';
      }
    };

    /**
     * @name 序号转换
     * @param {题目序号} sort
     */
    const convertSort = sort => {
      return arabToChinese(sort);
    };

    /**
     * @name 获取定位点状态
     * @param {当前页码} page
     */
    const getPosPointState = page => {
      return DOMOperations.getPosPointState(props.data.pageLayout, page);
    };

    const getQuesContentH = el => {
      if (!el) return 0;
      let quesH = 0;
      Array.from(el?.children).forEach(ele => {
        if (!Array.from(ele.classList).includes('left')) {
          quesH += ele.offsetHeight;
        }
      });
      return pxConversionMm(quesH);
    };

    const replaceOtherClass = str => {
      return str.replace(/ |drag|over/g, '');
    };

    /**
     * @name 合并分割节点
     */
    const mergeDom = () => {
      const sqArr = document.querySelectorAll('.q-opt:not(.split-ques)');
      Array.from(sqArr).forEach(el => {
        let splitEls = document.querySelectorAll(`[id="${el.id}"]`);
        let sourceEl; //最后元素为原始标签
        splitEls = Array.from(splitEls).filter(item => {
          if (!item.className.includes("split-ques")) {
            sourceEl = item
          }
          return item.className.includes("split-ques")
        })
        if (!sourceEl || !splitEls.length) return;
        let sourceH = sourceEl.getElementsByClassName('ques-box')[0]?.offsetHeight || 0;
        sourceH = sourceH < 5 ? 0 : sourceH;

        if (!sourceEl.getElementsByClassName('ques-box')[0]?.offsetHeight) {
          Array.from(sourceEl.getElementsByClassName('ques-box')[0]?.children || []).forEach(el => {
            sourceH += el.offsetHeight;
          });
        }

        let quesH = 0;
        splitEls
          .reverse()
          .forEach(el => {
            quesH += el.offsetHeight;
            if (el.getElementsByClassName('score-table').length) {
              quesH = quesH - el.getElementsByClassName('score-table')[0].offsetHeight;
            }
            if (el.getElementsByClassName('ques-content-name').length) {
              quesH = quesH - el.getElementsByClassName('ques-content-name')[0].offsetHeight;
            }
            try {
              let tagEls = [];
              let score = el.getElementsByClassName('score-table');
              if (!score.length) {
                score = el.getElementsByClassName('write-score-box');
              }
              if (score.length) {
                tagEls.push(score[0].parentElement);
              }
              const ques = el.getElementsByClassName('ques-box');
              if (ques.length) {
                Array.from(ques[0].children).forEach(p => {
                  // if(p.nodeName == "DIV"){
                  //   Array.from(p.children).forEach((span) => {
                  //     tagEls.push(span)
                  //   })
                  // }else{
                  tagEls.push(p);
                  // }
                });
              }

              // if(tagEls.length==0){
              //   tagEls = el.getElementsByClassName("remove-ques")
              // }
              // let tagH = 0;
              for (let i = tagEls.length - 1; i >= 0; i--) {
                const tag = tagEls[i];
                // if(tag.className.indexOf('score-container')<0){
                //   // tagH += tag.offsetHeight;
                //   tagH = Math.max(tag.offsetHeight + tag.offsetTop,tagH);
                // }
                // tag.className = tag.className.replace("remove-ques", "");
                let tagParent = tag.parentElement;

                let target;
                //标记是否本级
                let isChilds = false;
                while (!target) {
                  if (sourceEl.getElementsByClassName(replaceOtherClass(tag.className)).length) {
                    //查找本级classname是否相同
                    target = sourceEl.getElementsByClassName(replaceOtherClass(tag.className))[0];
                    isChilds = true;
                  } else if (
                    //查找父级classname是否相同
                    sourceEl.getElementsByClassName(replaceOtherClass(tagParent.className)).length
                  ) {
                    target = sourceEl.getElementsByClassName(
                      replaceOtherClass(tagParent.className)
                    )[0];
                    isChilds = false;
                  } else if (tagParent.className.indexOf('ques-box') >= 0) {
                    target = sourceEl.getElementsByClassName('ques-box')[0];
                    isChilds = false;
                  }
                  tagParent = tagParent.parentElement;
                }
                if (isChilds) {
                  Array.from(tag.children)
                    .reverse()
                    .forEach(el => {
                      target.insertBefore(el, target.firstChild);
                    });
                } else {
                  target.insertBefore(tag, target.firstChild);
                }
              }
            } catch (error) {
              console.warn(error);
            }
          });
        const _h = Math.round(pxConversionMm(sourceH + quesH));
        sourceEl.getElementsByClassName('ques-box')[0]?.style.height &&
          (sourceEl.getElementsByClassName('ques-box')[0].style.height = _h ? _h + 'mm' : '');
      });
    };

    /**
     * @name 移除自定义插入的节点
     */
    const removeDom = () => {
      DOMOperations.removeCustomDivs();
    };
    /**
     * @name 创建占位标签
     * @param {高度} height
     */
    const createEmpty = (height) => {
      return PaginationUtils.createEmptyElement(height);
    };

    /**
     * @name 创建密封区
     */
    const createHandWrite = () => {
      return DOMOperations.createHandWriteArea();
    };

    /**
     * @name 创建页头标签
     * @param {当前页码} pageNum
     */
    const createHeader = (pageNum) => {
      const context = {
        layout: props.data.pageLayout,
        numberLayout: props.data.numberLayout,
        pageNum
      };
      return DOMOperations.createHeader(context);
    };

    /**
     * @name 创建基础信息标签
     * @param {当前页码} pageNum
     */
    const createHeaderInfo = (pageNum) => {
      const context = {
        layout: props.data.pageLayout,
        numberLayout: props.data.numberLayout,
        pageNum
      };
      return DOMOperations.createHeaderInfo(context);
    };

    // 使用统一的布局工具类，移除重复的配置对象

    /**
     * @name 创建页脚标签
     * @param {当前页码} pageNum
     * @param {总页数} totalPage
     */
    const createFooter = (pageNum, totalPage = pageNum) => {
      const context = {
        layout: props.data.pageLayout,
        numberLayout: props.data.numberLayout,
        pageNum
      };

      return DOMOperations.createFooter(context, totalPage);
    };

    const createLine = (pageNum) => {
      return PaginationUtils.createPageLine(pageNum);
    };

    const renderCard = async el => {
      // 使用新的渲染管理器
      const context = {
        layout: props.data.pageLayout,
        numberLayout: props.data.numberLayout,
        spreadQues: props.data.spreadQues,
        cardType: props.data.cardType
      };

      await RenderManager.renderCard(el, context);

      // 所有分页逻辑已移至 RenderManager 中处理
    };

    // 加载跨页切分题目编辑器 - 已移至 RenderManager 中处理
    const loadSplitQuesCke = () => {
      // 此函数已被 RenderManager 接管，保留空实现以兼容现有调用
    };
    const handleUpdateQustions = async () => {
      //不通过vue绑定题目名称变化，避免题目重新渲染导致高度还原
      Paper.quesInfosInCard.forEach((ques) => {
        $('#name_' + ques.id).html(ques.htmlname || ques.name);
      })
    };

    const handleSwitchLayout = async () => {
      removeStyle();
      reloadCard(false);
    };

    // 设置题目数据
    const setQuesInfos = async () => {
      state.quesInfos = Paper.quesInfosInCard;
      setTimeout(() => {
        renderCard();
      }, 500);
      // reloadCard(true)
    };

    let loading = null;
    const handleOrderQuesInfos = async () => {
      reloadCard(true);
    };

    const renderOptions = () => {
      if (props.data.pageLayout == IPAGELAYOUT.A32 || props.data.pageLayout == IPAGELAYOUT.A23) {
        setTimeout(() => {
          bus.emit('renderOpts');
          //异型卡延迟处理选项，避免宽度计算问题
        }, 2000);
      } else {
        bus.emit('renderOpts');
      }
    };

    const changeName = async (e, item) => {
      item.name = e.target.innerText;
      item.htmlname = e.target.innerHTML;
      // reloadCard(true)
    };

    const mathjaxLoad = (fn) => {
      if (props.data.isEnglishSubject()) {
        fn()
      } else {
        const MathJax = window.MathJax;
        if (MathJax && MathJax.Hub) {
          MathJax.Hub.Queue(['Typeset', MathJax.Hub], function () {
            fn()
          });
        } else {
          fn(); // 如果MathJax不可用，直接执行回调
        }
      }
    }

    /**
     * 延迟执行回调函数，直到指定条件成立。
     * @param {Function} condition 检查条件的函数，当条件返回true时，执行回调。
     * @param {Function} callback 条件满足时执行的回调函数。
     * @param {number} [interval=500] 检查条件的间隔时间，单位为毫秒。
     */
    const delayUntil = (condition, callback, interval = 500) => {
      let intervalId;

      const checkConditionAndExecute = () => {
        if (condition()) {
          if (intervalId) clearInterval(intervalId);
          callback();
        }
      };

      intervalId = setInterval(checkConditionAndExecute, interval);
      setTimeout(() => {
        if (intervalId) clearInterval(intervalId);
      }, 15000);
    };

    onBeforeMount(() => {
      state.quesInfos = Paper.quesInfosInCard;
    });

    onBeforeUnmount(() => {
      Paper.off("updateQustions", handleUpdateQustions);
      Paper.off('changeNumberLayout', handleChangeNumberLayout);
      bus.off('renderAllCard', renderCard);
      bus.off('switchLayout', handleSwitchLayout);
      bus.off('updateQuesInfos', setQuesInfos);
      bus.off('orderQuesInfos', handleOrderQuesInfos);
      bus.off('reRenderMathJax', handleReRenderMathJax);
    });

    onMounted(() => {
      sessionStorage.setItem('queLoadCount', 0);
      Paper.on("updateQustions", handleUpdateQustions);
      Paper.on('changeNumberLayout', handleChangeNumberLayout);
      bus.on('reRenderMathJax', handleReRenderMathJax);
      bus.on('renderAllCard', renderCard);
      bus.on('switchLayout', handleSwitchLayout);
      bus.on('updateQuesInfos', setQuesInfos);
      bus.on('orderQuesInfos', handleOrderQuesInfos);

      $('.page-box').on('blur', '.q-item-box', function (e) {
        const qid = e.currentTarget.id;
        bus.emit("changeQuesHtml", qid)
      })

      loading = ElLoading.service({
        lock: true,
        text: '题目排版中...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      if (props.data.cardType == ICARDMODEL.ONLYCARD && !props.data.quesInfos.length) {
        mathjaxLoad(() => {
          renderCard();
          // removeComment();
          loading.close();
        })
      } else {
        let isLoad = false;
        const initRender = () => {
          if (isLoad) return;
          isLoad = true;
          console.time('所有公式渲染完成');
          mathjaxLoad(() => {
            console.timeEnd('所有公式渲染完成');
            Paper.trigger('changeLayout', 0);
            setTimeout(() => {
              renderCard();
              renderOptions();
              // removeComment();
              loading.close();
            }, 200);
          })
        };
        //题目加载完成数与题目总数相等时执行
        const myCondition = () =>
          Number(sessionStorage.getItem('queLoadCount')) ==
          Object.keys(props.data.questions).length;
        delayUntil(
          myCondition,
          function () {
            initRender();
          },
          1000
        );
        //确保上面条件无法满足后，依然正常执行渲染分页 保底策略
        setTimeout(async () => {
          initRender();
        }, 15000);
      }
    });

    return {
      ...toRefs(state),
      isHideQues,
      convertSort,
      removeDom,
      renderCard,
      getPosPointState,
      IPAGELAYOUT,
      QUES_TYPE,
      INUMBERLAYOUT,
      headerHeight,
      getHeaderInfoHeight,
      setQuesInfos,
      changeName,
    };
  },
});
</script>

<style lang="scss">
.ck-powered-by {
  display: none;
}

.a33,
.a32,
.a23 {
  .page-box>div {
    width: 130mm;
  }
}

.page-box {
  position: relative;
  width: 196mm;
  //   width: 180mm;
  min-height: 297mm;
  height: auto;
  margin: 0 auto;
  // padding: 0px 2mm;
  background: #fff;
  font-size: var(--fontSize);
  font-family: var(--fontFamily);
  line-height: var(--lineHeight);

  .footer {
    position: absolute;
    left: 0;
    right: 0;
    width: 100%;
    height: 14mm;
    padding: 0;
    text-align: center;
  }

  .page-line {
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    height: 1px;
    width: 100%;
    background: #000;
    z-index: 2;
  }
}

.line-item.el-select {
  position: relative;

  .el-select-dropdown {
    position: absolute !important;
  }
}

.custom-div {
  position: relative;

  &.empty {
    position: relative;
    z-index: 100;
  }

  &.footer {
    z-index: 99;
  }

  &.splitor-empty {
    +.splitor-empty {
      display: none;
    }
  }
}

.fill-item-popover {
  padding: 0 !important;

  .el-popover__title {
    padding: 10px 15px 0;
    text-align: center;
  }
}
</style>
