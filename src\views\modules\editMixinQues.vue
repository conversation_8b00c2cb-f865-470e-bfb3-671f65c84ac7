<!-- 添加/编辑混合题 -->
<template>
  <div class="c30-ques-mixin-modal noselect">
    <el-dialog v-model="isShowDialog" :title="`${mode === 'add' ? '增加' : '编辑'} 混合题 大题`" :close-on-click-modal="false"
      destroy-on-close :before-close="closeDialog" class="el-dialog--big" draggable>
      <el-form class="wh100" ref="formRef" :model="baseForm" label-width="90px">
        <el-container class="wh100">

          <el-aside class="from-base-col" width="300px">
            <div class="from-base-wrap">
              <el-form-item label="大题名称" prop="name" :rules="rules.name">
                <el-input v-model="baseForm.name"></el-input>
              </el-form-item>

              <el-form-item label="名称显示">
                <el-switch v-model="baseForm.showName" inline-prompt active-text="是" inactive-text="否" active-value="1"
                  inactive-value="0" />
              </el-form-item>

              <el-form-item v-if="isBlankCard" label="小题数量">
                <el-input-number v-model="baseForm.count" :min="1" @change="changeQuesNum" size="small" />
              </el-form-item>
            </div>
          </el-aside>

          <el-main class="form-table">

            <el-table ref="formTable" row-key="id" table-layout='auto' max-height="550" :data="quesFlatList"
              default-expand-all @current-change="handleHiglightRow">

              <el-table-column prop="quesName" label="题号" align="center">
                <template class="text" #default="scope">

                  <el-input v-model="scope.row.quesName" @input="onChangeQuesName(scope.row)"
                    class="sort-input"></el-input>
                </template>
              </el-table-column>

              <el-table-column prop="type" label="题型" align="center">
                <template #default="scope">
                  <span v-if="!scope.row.children">{{ scope.row.name }}</span>
                  <el-select v-else v-model="scope.row.typeId" @change="changeQuesType(scope.row)"
                    :disabled="!isBlankCard" placeholder="请选择">
                    <el-option v-for="item in QuesTypeList" :key="item.type" :label="item.name" :value="item.type" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="type" width="110" label="小问数" align="center">
                <template #default="scope">
                  <span v-if="!scope.row.children"> -- </span>
                  <span v-else-if="!isBlankCard">{{ scope.row.count }}</span>
                  <el-input-number :min="1" v-model="scope.row.count"
                    @input="changeSmallQuesChildLength($event, scope.row)" class="score-input" controls-position="right"
                    v-else />
                </template>
              </el-table-column>

              <el-table-column prop="optionCount" width="90" label="选项个数" align="center">
                <template #default="scope">
                  <span
                    v-if="!scope.row.typeId || (scope.row.typeId != QUES_TYPE.choice && scope.row.typeId != QUES_TYPE.singleChoice)">
                    -- </span>
                  <span v-else-if="!isBlankCard || !scope.row.children">{{ scope.row.optionCount }}</span>

                  <el-select v-else v-model="scope.row.optionCount" @change="setSmallQuesContent(scope.row)">
                    <template v-for="idx of Paper.optionCountMax" :key="idx">
                      <el-option :label="idx" :value="idx" v-if="idx > 1">
                      </el-option>
                    </template>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column prop="quesScore" label="分值" align="center">
                <template #default="scope">
                  <el-input v-model="scope.row.score" class="score-input"
                    @input="caculTotalScoreChildren(scope.row)"></el-input>
                </template>
              </el-table-column>

              <el-table-column fixed="right" width="80" label="操作" align="center" v-if="isBlankCard">
                <template #default="scope">
                  <el-button v-if="!scope.row.children" type="link" size="small"
                    @click="deleteSmallQuesChildren(scope.row)" :disabled="baseForm.data.length === 1">删除</el-button>
                  <el-button v-else type="link" size="small" @click="deleteSmallQues(scope.row)"
                    :disabled="baseForm.data.length === 1">删除</el-button>
                </template>
              </el-table-column>

            </el-table>

            <div style="padding:10px 15px;">
              <el-button class="btn-add-small" type="primary" size="small" @click="handleAddSmallQues"
                v-if="isBlankCard">+
                增加小题</el-button>
              <div class="pull-right">总分值：{{ baseForm.score }}</div>
            </div>
          </el-main>
        </el-container>

      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button size="large" type="default" @click="closeDialog">
            <slot name="confirmText">取消</slot>
          </el-button>
          <el-button size="large" type="primary" @click="confirmSubmit" :loading="loading" style="margin-right: 30px">
            <slot name="cancleText">确定</slot>
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, nextTick } from 'vue';
import {
  QUES_TYPE,
  JUDGE_TYPE,
  GRID_TYPE,
  GRIDPLACE_TYPE,
  MARK_TYPE,
  DECIMAL_TYPE,
  ISmallQues,
  LINE_WIDTH,
  ICARDMODEL,
  IBigQues,
  IRULERTYPE,
} from '@/typings/card';
import { arabToChinese, checkFloatNum, checkIsNumber, deepClone, generateUUID } from '@/utils/util';

import Paper from '../paper';
import { PaperConstant } from '../paper.constant';
import { ElMessage } from 'element-plus';
export default defineComponent({
  props: {
    //最后一题下标
    index: {
      type: Number,
      default: 0,
    },
    //弹框是否显示
    isShowDialog: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: 'add',
    },
  },
  emits: ['close-dialog', 'update-question'],
  components: {},
  setup(props: any, ctx: any) {
    // 表格表单对象
    const formTable = ref<any>(null);
    //表单对象
    const formRef = ref<any>(null);

    const state = reactive({
      Paper: Paper,
      //题目类型
      QUES_TYPE: QUES_TYPE,
      //判断题样式类型
      JUDGE_TYPE: JUDGE_TYPE,
      //判分格位置类别
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      //打分类型
      MARK_TYPE: MARK_TYPE,
      //十个位是否分开
      GRID_TYPE: GRID_TYPE,
      //分数精度类别
      DECIMAL_TYPE: DECIMAL_TYPE,
      LINE_WIDTH: LINE_WIDTH,
      ICARDMODEL: ICARDMODEL,
      isShowView: false,
      //提交按钮加载中状态
      loading: false,
      //表单模型
      baseForm: {
        id: generateUUID(),
        quesNos: '',
        isChangeSort: true,
        name: '',
        type: '混合题',
        typeId: 9,
        isAverage: false,
        isChange: false,
        showName: '1',
        quesScore: 5,
        score: 5,
        count: 0,
        optionCount: 4,
        arrange: 1,
        halfScore: 2.5,
        mixinMode: true,
        data: [],
        ruleType: IRULERTYPE.STANDAD,
      } as any,
      // 题卡模式需要记住大题的真实类型
      bigQuesRealTypeId: '',
      // 表单验证规则
      rules: {
        quesType: [
          {
            required: true,
            message: '必填项',
            trigger: 'change',
          },
        ],
        name: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
        ],
        // 数字和必填项验证规则
        numberRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          { validator: checkIsNumber, trigger: 'blur' },
        ],
        // 数字 可以是小数
        floatNumRules: [
          {
            required: true,
            message: '必填项',
            trigger: 'blur',
          },
          {
            validator: checkFloatNum,
            trigger: 'blur',
          },
        ],
      },
      QuesTypeList: PaperConstant.QuesTypeList.filter(item => {
        return (
          item.type !== QUES_TYPE.mixin &&
          item.type !== QUES_TYPE.noAnswerArea &&
          item.name !== '作文题'
        );
      }),
      //已有的小题最后一个题号
      lastSmallQuesIndex: 0,
      // 高亮的一行
      handleHiglightRowIndex: -1,
      // 混合题编辑前长度
      quesLengthBeforeEdit: 0,
      // 混合题子集列表，扁平化处理题型
      quesFlatList: [],
      isBlankCard: Paper.cardType == ICARDMODEL.BLANKCARD,
    });

    /**
     * @name:关闭弹窗
     */
    const closeDialog = () => {
      state.loading = false;
      ctx.emit('close-dialog');
    };

    const handleHiglightRow = (val: any) => { };

    const createSmallQues = () => {
      let quesNos = state.lastSmallQuesIndex++;
      let lastQues = state.quesFlatList[state.quesFlatList.length - 1];
      if (lastQues) {
        quesNos = Number(lastQues.quesNos) + 1;
      }
      return {
        id: generateUUID(),
        parentId: state.baseForm.id,
        // 在混合题子集的索引
        childIndex: state.quesFlatList.length,
        quesNos,
        quesName: quesNos,
        score: 5,
        quesScore: 5,
        name: '',
        typeId: QUES_TYPE.subject,
        type: '',
        isChange: false,
        answer: '',
        optionCount: 0,
        // 小问数
        count: 1,
        data: [],
        children: [],
      };
    };

    /**
     * @name: 修改小题数量
     */
    const changeQuesNum = () => {
      let lastLength = state.quesFlatList.length;
      let needCreatLength = state.baseForm.count - lastLength;
      if (needCreatLength == 0) return;

      if (needCreatLength > 0) {
        // 增加
        for (let i = 0; i < needCreatLength; i++) {
          let smallQues = createSmallQues();
          state.quesFlatList.push(smallQues);
          changeQuesType(smallQues);
        }
      } else {
        // 减少
        state.quesFlatList = state.quesFlatList.splice(0, state.baseForm.count);
      }

      caculTotalScore();
    };

    /* 处理增加小题事件 */
    const handleAddSmallQues = () => {
      state.baseForm.count++;
      let smallQues = createSmallQues();
      state.quesFlatList.push(smallQues);
      caculTotalScore();

      changeQuesType(smallQues);
    };

    /* 删除小题 */
    const deleteSmallQues = (row: any) => {
      if(state.quesFlatList.length == 1) return;
      state.baseForm.count--;
      state.quesFlatList.splice(row.childIndex, 1);
      state.quesFlatList.forEach((item: any, index) => {
        item.childIndex = index;
      });
      caculTotalScore();
    };

    /* 删除小题小问 */
    const deleteSmallQuesChildren = (row: any) => {
      let parentData = state.quesFlatList.filter(item => item.id == row.parentId)[0];
      let index = parentData.data.findIndex(item => item.id === row.id);

      parentData.data.splice(index, 1);
      if (parentData.data.length === 0) {
        parentData.typeId = '';
      } else if (parentData.data.length === 1) {
        parentData.data[0].quesName = parentData.quesNos;
        parentData.data[0].quesNos = parentData.quesNos;
        parentData.children = [];
        if (parentData.typeId != QUES_TYPE.subject) {
          parentData.data[0].quesName = ' ';
        }
      }
      parentData.count = parentData.data.length;
      caculTotalScoreChildren(row);
    };

    /* 改变小题父级题号 */
    const changeFormQuesNo = () => {
      state.quesFlatList.forEach((item: any, index) => {
        item.quesName = `${Number(state.baseForm.quesNos)}(${index + 1})`;
      });
    };

    /* 设置小问信息 */
    const setSmallQuesContent = (item: any) => {
      if (!item.typeId) {
        caculTotalScore();
        return;
      }

      item.data = item.data.splice(0, Number(item.count));
      item.score = item.quesScore * item.count;
      let smallCount = 0;
      const quesTypeName = state.QuesTypeList.find((subItem: any) => {
        return item.typeId == subItem.type;
      }).name;
      do {
        let quesNum = `(${smallCount + 1})`;
        let quesNos = item.quesNos + quesNum;
        let quesName = quesNum;
        if (item.typeId == QUES_TYPE.subject) {
          if (item.count > 1) {
            quesName = quesNos;
          } else {
            quesNos = quesName = item.quesNos;
          }
        } else if (item.count <= 1) {
          quesName = item.quesNos;
          quesNos = item.quesNos;
        }

        let _small: any = {
          id: generateUUID(),
          parentId: item.id,
          name: quesTypeName,
          type: quesTypeName,
          typeId: item.typeId,
          quesNos,
          quesName,
          markType: item.markType,
          score: 5,
          halfScore: 2.5,
          isChange: false,
          answer: '',
          arrange: 0,
          isSplited: item.typeId == QUES_TYPE.subject,
        };
        if (Paper.isChoiceQues(item.typeId)) {
          // 选择题
          _small.arrange = Paper.arrange;
          _small.optionCount = item.optionCount;
          resetHalfScoreRules(item);
        } else if (Paper.isFillQues(item.typeId)) {
          // 填空题
          _small.isShowSetting = false;
          _small.lineType = Paper.lineType;
          _small.lineNum = Paper.lineNum;
        } else if (item.typeId == QUES_TYPE.subject) {
          // 简答题
          _small.hasLine = Paper.hasLine;
          _small.rowNum = Paper.rowNum;
          _small.cellSize = Paper.cellSize;
          _small.decimal = Paper.decimal;
        } else if (item.typeId == QUES_TYPE.judge) {
          _small.judgeType = item.judgeType;
        }

        item.data[smallCount] = _small;
        smallCount++;
      } while (smallCount < item.count);
      caculTotalScore();
      item.children = item.count > 1 ? item.data : [];
    };

    /* 计算总分值，计算半对分值 */
    const caculTotalScore = () => {
      let firstMixinData = state.quesFlatList[0];

      if (state.quesFlatList.length > 1) {
        state.baseForm.score = 0;
        state.quesFlatList.forEach(item => {
          state.baseForm.score = (state.baseForm.score * 10 + Number(item.score) * 10) / 10;
          item.halfScore = Math.round((Number(item.score) / item.data.length / 2) * 100) / 100;

          if (item.typeId == QUES_TYPE.choice) {
            item.children.forEach(subItem => {
              subItem.halfScore = Math.round((Number(subItem.score) / 2) * 10) / 10;
            });
          }
        });
      } else {
        state.baseForm.score = Number(firstMixinData.score);
        if (firstMixinData.typeId == QUES_TYPE.choice) {
          firstMixinData.children.forEach(subItem => {
            subItem.halfScore = Math.round((Number(subItem.score) / 2) * 10) / 10;
          });
        }
      }
      state.baseForm.quesScore = state.baseForm.score;
    };

    /* 改变小问数量 */
    const changeSmallQuesChildLength = async (val: number, row: any) => {
      row.count = val;
      setSmallQuesContent(row);
      resetHalfScoreRules(row);
    };

    /* 设置小问分值 */
    const caculTotalScoreChildren = (row: any) => {
      let score = row.score;
      score = score.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '');
      let parts = score.split('.');
      if (parts.length > 1) {
        parts = parts.splice(0, 2);
        // 如果有小数点
        parts[1] = parts[1].slice(0, 1); // 仅保留一位小数
      }
      score = parts.join('.');
      if (score > 100) {
        score = 100
      }
      row.score = score;
      if (row.children && row.children.length) {
        // 平均修改小题分数
        row.children.forEach((rowItem: any) => {
          rowItem.score = Math.round(row.score / row.children.length);
        });
      } else {
        let parentData = state.quesFlatList.filter(item => item.id == row.parentId)[0];
        if (!parentData) {
          row.data[0].score = row.score;
        } else {
          if (parentData.children.length > 1) {
            let totalScore = parentData.children.reduce((prev, curr) => {
              const currScore = Number(curr.score) * 10;
              const preScore = Number(
                typeof prev == 'number' || typeof prev == 'string' ? prev : prev.score
              );

              return Number(preScore) + currScore;
            }, 0);
            parentData.score = totalScore / 10;
          } else {
            parentData.score = parentData.data[0].score;
          }
        }
      }

      caculTotalScore();
      resetHalfScoreRules(row);
    };

    /* 改变小标题 */
    const onChangeQuesName = (row: any) => {
      if (row.children && row.children.length > 1) {
        if (row.quesName) {
          row.quesNos = row.quesName;
        }

        if (row.typeId == QUES_TYPE.subject) {
          row.children.forEach((rowItem: any) => {
            rowItem.quesName = rowItem.quesName.replace(
              /.+?(?=[\(\（])|(^|[^0-9])(?=[\(\（])/g,
              row.quesName
            );
            rowItem.quesNos =
              rowItem.quesName.indexOf(row.quesName) === 0
                ? rowItem.quesName
                : row.quesName + rowItem.quesName;
          });
        }
      } else {
        if (row.data) {
          row.quesNos = row.quesName;
          row.data[0].quesName = row.quesName;
          row.data[0].quesNos = row.quesName;
        } else {
          let bigQues = state.quesFlatList.find(item => item.id === row.parentId);
          row.quesNos = bigQues.quesNos + row.quesName;
        }
      }
    };

    /* 改变题型 */
    const changeQuesType = (item: any) => {
      const quesName = state.QuesTypeList.filter((subItem: any) => {
        return item.typeId == subItem.type;
      })[0].name;
      item.name = quesName;
      item.type = quesName;
      item.showName = '1';

      if (item.typeId == QUES_TYPE.singleChoice) {
        // 单选题
        item.arrange = Paper.arrange;
        item.optionCount = 4;
        item.showType = 'vertical';
        item.verticalLines = 5;
        item.quesNos = item.quesName;
      } else if (item.typeId == QUES_TYPE.choice) {
        //多选题
        item.halfScore = 2.5;
        item.data.forEach(subItem => (subItem.halfScore = item.halfScore));
        item.arrange = Paper.arrange;
        item.optionCount = 4;
        item.ruleType = IRULERTYPE.STANDAD;
        item.quesNos = item.quesName;
      } else if (item.typeId == QUES_TYPE.judge) {
        //判断题
        item.judgeType = Paper.judgeType;
        item.markType = MARK_TYPE.YESORNO;
      } else if (Paper.isFillQues(item.typeId)) {
        //填空题
        item.step = Paper.step;
        item.markType = Paper.markType;
        item.gridPlace = Paper.gridPlace;
        item.arrange = Paper.arrange;
        item.lineType = Paper.lineType;
        item.fillDistance = 10;
      } else if (item.typeId == QUES_TYPE.subject) {
        //简答题
        item.step = Paper.step;
        item.markType = Paper.markType;
        item.gridType = Paper.gridType;
        item.decimal = Paper.decimal;
        item.hasLine = Paper.hasLine;
        item.wordNumber = Paper.wordNumber;
        item.isWriting = Paper.isWriting;
        item.cellSize = Paper.cellSize;
        item.rowNum = Paper.rowNum;
        item.cells = Paper.cells;
        item.hasTitle = false;
      }

      setSmallQuesContent(item);
    };

    /**
     * @name:检查表格表单是否合法
     */
    const checkTableFormValidate = (): { valid: boolean; message: string } => {
      for (const item of state.quesFlatList) {
        if (!item.typeId)
          return {
            valid: false,
            message: `请选择${item.quesName}题型类型 !`,
          };
      }

      return {
        valid: true,
        message: '验证通过！',
      };
    };

    /**
     * @name:确定提交题目数据
     */
    const confirmSubmit = () => {
      state.loading = false;
      formRef.value.validate((valid: boolean, message: string) => {
        if (!valid) {
          ElMessage({
            message: message,
            type: 'error',
          });
          return;
        }

        let { valid: isTableFormValid, message: _message } = checkTableFormValidate();
        if (!isTableFormValid) {
          ElMessage({
            message: _message,
            type: 'error',
          });
          return;
        }

        // 提交数据之前，清理children对象
        state.quesFlatList.forEach(item => delete item.children);
        state.baseForm.data = state.quesFlatList;
        let updateIndex = 0;
        let quesDatas = [state.baseForm, ...state.quesFlatList];
        // 更新父页面题目数据
        if (props.mode == 'add') {
          updateIndex = Paper.quesInfos.length;
          Paper.spliceQuestions(updateIndex, 0, quesDatas);
        } else {
          updateIndex = props.index;

          if (state.isBlankCard) {
            // 空白题卡
            Paper.spliceQuestions(updateIndex, state.quesLengthBeforeEdit + 1);
            Paper.spliceQuestions(updateIndex, 0, quesDatas);
          } else {
            // 题卡分离模式手动转换混合题
            delete state.baseForm.mixinMode;
            state.baseForm.typeId = state.bigQuesRealTypeId;
            state.quesFlatList.forEach(item => {
              delete item.parentId;
              delete item.children;
              delete item.count;

              if (item.data.length === 1) delete item.data;
            });
            state.baseForm.data = state.quesFlatList;
            Paper.spliceQuestions(updateIndex, 1, [state.baseForm]);
          }
        }
        ctx.emit('update-question', deepClone(state.baseForm), updateIndex, props.mode);
        closeDialog();
      });
    };

    /**
     * @name: 重置半对判分规则
     */
    const resetHalfScoreRules = item => {
      if (item.ruleType != IRULERTYPE.CUSTOM) return;
      item.ruleType = IRULERTYPE.STANDAD;
      delete item.rules;
      item.data.forEach((sitem: ISmallQues) => {
        sitem.ruleType = IRULERTYPE.STANDAD;
        //修改半对分值 则自定义分值规则失效
        delete sitem.rules;
      });
    };

    /**
     * @name:初始化数据
     */
    const initData = () => {
      if (props.mode === 'add') {
        // 添加模式，自动生成两个小题
        state.baseForm.name = arabToChinese(Number(props.index + 1)) + '、' + state.baseForm.type;
        state.lastSmallQuesIndex = Paper.getSmallQuesLength() + 1;

        handleAddSmallQues();
        handleAddSmallQues();
      } else {
        // 编辑模式，抓取扁平列表中的混合题，标记parentId
        state.baseForm = deepClone(Paper.quesInfos[props.index]);
        if (state.isBlankCard) {
          // 空白题卡
          let id = state.baseForm.id;
          let quesInfos = deepClone(Paper.quesInfos);
          // 补充children信息
          quesInfos.forEach(item => {
            if (item.data.length <= 1) {
              item.children = [];
              return;
            }

            item.children = item.data;
          });
          state.quesFlatList = quesInfos.filter(item => {
            item.typeId = Number(item.typeId);
            return item.parentId === id
          });
        } else {
          // 题卡分离模式混合题手动转换
          state.bigQuesRealTypeId = state.baseForm.typeId;
          state.baseForm.typeId = QUES_TYPE.mixin;
          state.baseForm.mixinMode = true;
          state.quesFlatList = state.baseForm.data;
          state.quesFlatList.forEach(item => {
            item.typeId = Number(item.typeId);
            item.parentId = state.baseForm.id;
            // 混合体内不应该存在智批题，强制修改
            if (Paper.isFillQues(item.typeId)) {
              item.typeId = QUES_TYPE.fill;
            }
            if (!item.quesName) item.quesName = item.quesNos;

            if (!item.data || item.data.length <= 1) {
              item.children = [];
              item.data = [deepClone(item)];
              item.count = 1;
              return;
            }

            item.children = item.data;
            if (!item.count) item.count = item.children.length;
          });
        }
        state.lastSmallQuesIndex =
          Number(state.quesFlatList[state.quesFlatList.length - 1].quesNos) + 1;
        state.quesLengthBeforeEdit = state.quesFlatList.length;
        state.baseForm.count = state.quesFlatList.length;
      }
    };

    initData();

    return {
      ...toRefs(state),
      formRef,
      formTable,
      closeDialog,
      confirmSubmit,
      changeQuesNum,
      changeQuesType,
      caculTotalScore,
      deleteSmallQues,
      changeFormQuesNo,
      onChangeQuesName,
      handleHiglightRow,
      handleAddSmallQues,
      setSmallQuesContent,
      deleteSmallQuesChildren,
      caculTotalScoreChildren,
      changeSmallQuesChildLength,
    };
  },
});
</script>

<style lang="scss" scoped>
.c30-ques-mixin-modal {
  .from-base-col {
    padding: 0 30px 0 15px !important;
    border-right: 1px dotted #d8d8d8;
    min-height: 480px;
  }

  .from-base-wrap {
    padding-top: 15px;
  }

  .el-dialog__headerbtn {
    right: 30px;
  }

  .el-input-number {
    width: 140px !important;
  }

  .el-form-item {
    margin-bottom: 17px !important;
  }

  ::v-deep {
    .el-table__placeholder {
      display: none;
    }

    .el-dialog__body {
      border-bottom: 1px solid #d8d8d8 !important;
    }

    .from-base-wrap {
      .el-form-item__content {
        color: #000 !important;
      }
    }

    .form-table {
      padding: 0;

      .el-form-item__content {
        margin-left: 20px !important;
        line-height: 30px !important;
      }

      .el-input__icon {
        line-height: 30px !important;
      }

      .el-table__expand-icon {
        margin-left: -25px;
      }

      .el-input {
        &.sort-input {
          width: 70px;
        }

        .el-input__inner {
          text-align: center;
          appearance: none;
        }
      }

      .el-table__row--level-1 {
        background-color: #f7f7f7b6;
      }
    }

    .el-col {
      height: 450px;
    }

    .el-dialog__footer {
      padding: 14px !important;
    }

    .el-table {
      color: #000 !important;
    }

    .el-table,
    .el-table__fixed-right {
      &::before {
        bottom: 2px;
      }
    }

    .el-table thead {
      color: #878787 !important;
    }

    .el-input__inner {
      color: #000 !important;
      height: 30px !important;
      line-height: 30px !important;
    }

    .el-input-number .el-input__inner {
      height: 32px !important;
      line-height: 32px !important;
    }

    .el-table__indent {
      display: none;
    }

    .el-table__body {
      .el-input-number {
        width: 85px !important;
      }
    }
  }
}
</style>
