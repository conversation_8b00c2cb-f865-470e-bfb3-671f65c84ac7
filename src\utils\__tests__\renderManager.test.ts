/*
 * @Description: RenderManager 测试用例
 * @Author: 优化重构
 * @Date: 2024-12-19
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { RenderManager } from '../renderManager';
import { IPAGELAYOUT, ICARDMODEL } from '@/typings/card';

// Mock DOM环境
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ({
    display: 'block',
    visibility: 'visible',
    opacity: '1'
  })
});

// Mock Vue render函数
vi.mock('vue', () => ({
  render: vi.fn(),
  h: vi.fn(),
  nextTick: vi.fn(() => Promise.resolve())
}));

// Mock bus
vi.mock('@/utils/bus', () => ({
  default: {
    emit: vi.fn()
  }
}));

describe('RenderManager', () => {
  let mockContainer: HTMLElement;
  let mockContext: any;

  beforeEach(() => {
    // 创建模拟的DOM容器
    mockContainer = document.createElement('div');
    mockContainer.className = 'page-box';
    
    // 添加一些测试内容
    const testElement = document.createElement('div');
    testElement.className = 'ques-item';
    testElement.style.height = '100px';
    mockContainer.appendChild(testElement);

    // 创建测试上下文
    mockContext = {
      layout: IPAGELAYOUT.A4,
      numberLayout: 1,
      spreadQues: 1, // 启用跨页分割
      cardType: ICARDMODEL.QUESCARD
    };

    // Mock document.body.getElementsByClassName
    document.body.getElementsByClassName = vi.fn(() => [mockContainer] as any);
  });

  it('应该正确处理跨页分割', async () => {
    // 创建一个需要分割的大元素
    const largeElement = document.createElement('div');
    largeElement.className = 'ques-box';
    largeElement.style.height = '300mm'; // 超过页面高度
    
    const childElement = document.createElement('div');
    childElement.className = 'ques-content';
    childElement.style.height = '150mm';
    largeElement.appendChild(childElement);
    
    mockContainer.appendChild(largeElement);

    // 执行渲染
    await RenderManager.renderCard(mockContainer, mockContext);

    // 验证是否创建了分割元素
    const splitElements = document.querySelectorAll('.split-ques');
    expect(splitElements.length).toBeGreaterThan(0);
  });

  it('应该正确处理A4两栏布局', async () => {
    mockContext.layout = IPAGELAYOUT.A4_TWO_COLUMN;

    await RenderManager.renderCard(mockContainer, mockContext);

    // 验证渲染完成
    expect(true).toBe(true); // 基础测试，确保没有抛出错误
  });

  it('应该在禁用跨页分割时不进行分割', async () => {
    mockContext.spreadQues = 0; // 禁用跨页分割

    await RenderManager.renderCard(mockContainer, mockContext);

    // 验证没有创建分割元素
    const splitElements = document.querySelectorAll('.split-ques');
    expect(splitElements.length).toBe(0);
  });
});
