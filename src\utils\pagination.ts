/*
 * @Description: 分页渲染工具类 - 处理页面分页和DOM操作逻辑
 * @Author: 优化重构
 * @Date: 2024-12-19
 */

import { IPAGELAYOUT } from '@/typings/card';
import { LayoutUtils } from './layout';
import { pageHeight, footerKeepHeight, getHeaderInfoHeight } from './config';
import { getHeight, pxConversionMm } from './dom';

/**
 * 分页上下文接口
 */
export interface IPaginationContext {
  layout: IPAGELAYOUT;
  pageSize: number;
  height: number;
  pageMainHeight: number;
  spreadQues: number;
}

/**
 * 分页计算结果接口
 */
export interface IPaginationResult {
  shouldPaginate: boolean;
  emptyHeight: number;
  prevHeight: number;
  scoreHeight: number;
  nameHeight: number;
}

/**
 * 分页渲染工具类
 */
export class PaginationUtils {
  /**
   * 计算页面主要内容高度
   * @returns 页面主要内容高度
   */
  static getPageMainHeight(): number {
    return pageHeight - footerKeepHeight;
  }

  /**
   * 判断是否需要分页
   * @param height 当前累计高度
   * @param pageMainHeight 页面主要内容高度
   * @returns 是否需要分页
   */
  static shouldPaginate(height: number, pageMainHeight: number): boolean {
    // 冗余2mm的高度避免高度精度误差
    return height - 1 > pageMainHeight;
  }

  /**
   * 计算分页相关高度信息
   * @param context 分页上下文
   * @param domHeight 当前DOM元素高度
   * @returns 分页计算结果
   */
  static calculatePaginationInfo(
    context: IPaginationContext,
    domHeight: number
  ): IPaginationResult {
    const { height, pageMainHeight } = context;
    const shouldPaginate = this.shouldPaginate(height, pageMainHeight);
    
    if (!shouldPaginate) {
      return {
        shouldPaginate: false,
        emptyHeight: 0,
        prevHeight: 0,
        scoreHeight: 0,
        nameHeight: 0
      };
    }

    const curHeight = height - domHeight;
    const emptyHeight = pageMainHeight - curHeight;

    return {
      shouldPaginate: true,
      emptyHeight,
      prevHeight: 0,
      scoreHeight: 0,
      nameHeight: 0
    };
  }

  /**
   * 计算元素高度信息
   * @param element DOM元素
   * @param emptyHeight 空白高度
   * @returns 高度计算结果
   */
  static calculateElementHeight(
    element: HTMLElement,
    emptyHeight: number
  ): { elHeight: number; scoreHeight: number; nameHeight: number; prevHeight: number } {
    let elHeight = pxConversionMm(element.offsetHeight + element.offsetTop);
    let scoreHeight = 0;
    let nameHeight = 0;
    let prevHeight = 0;

    // 如果只有一个子级且为图形，则获取图片高度
    if (element.children.length === 1 && 
        element.children[0].tagName === "IMG" && 
        (element.children[0] as HTMLElement).style.position !== "absolute") {
      elHeight = pxConversionMm((element.children[0] as HTMLElement).offsetHeight + element.offsetTop);
    }

    // 打分栏高度计算
    if (element.className.includes('score-table') ||
        (element.childElementCount === 1 && element.getElementsByClassName('score-table').length)) {
      scoreHeight = getHeight(element);
    } 
    // 纯答题卡题目名称高度计算
    else if (element.className.includes('ques-content-name')) {
      nameHeight = elHeight;
    } else {
      elHeight = scoreHeight + elHeight + nameHeight;
    }

    if (elHeight <= emptyHeight) {
      element.classList.add('remove-ques');
      prevHeight = Math.max(prevHeight, elHeight);
      return { elHeight, scoreHeight, nameHeight, prevHeight };
    }

    return { elHeight, scoreHeight, nameHeight, prevHeight };
  }

  /**
   * 检查是否可以拆分元素
   * @param element DOM元素
   * @returns 是否可以拆分
   */
  static canSplitElement(element: HTMLElement): boolean {
    if (element.nodeName === 'BR') return true;
    
    if (!element.children.length) return false;

    // 检查是否包含不可拆分的类
    const nonSplittableClasses = [
      'q-r-option', 'merge-img', 'writing-line', 
      'score-container', 'ck-math-tex', 'ques-item', 'ques-name-box'
    ];
    
    if (nonSplittableClasses.some(cls => element.classList.contains(cls))) {
      return false;
    }

    if (element.nodeName === 'TABLE') return false;

    // 检查是否包含文本节点
    const hasTextNode = Array.from(element.childNodes).some(
      node => node.nodeType === Node.TEXT_NODE && node.textContent?.trim() !== ''
    );

    if (hasTextNode) return false;

    // 检查特殊标签
    if ((element.nodeName === 'P' || element.nodeName === 'SPAN')) {
      const specialTags = ['sub', 'sup', 'u', 'font', 'br', 'img'];
      if (specialTags.some(tag => element.getElementsByTagName(tag).length)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取可见的子元素
   * @param element 父元素
   * @returns 可见的子元素数组
   */
  static getVisibleChildren(element: HTMLElement): HTMLElement[] {
    return Array.from(element.children).filter(item => {
      const htmlItem = item as HTMLElement;
      return (
        (htmlItem.nodeType === Node.ELEMENT_NODE &&
          (getHeight(htmlItem) || htmlItem.classList.contains('score-container')) > 0 &&
          !htmlItem.className.includes('nosave') &&
          this.isElementVisible(htmlItem)) ||
        htmlItem.nodeName === 'BR'
      );
    }) as HTMLElement[];
  }

  /**
   * 检查元素是否可见
   * @param element DOM元素
   * @returns 是否可见
   */
  private static isElementVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return style.display !== 'none' && 
           style.visibility !== 'hidden' && 
           style.opacity !== '0';
  }

  /**
   * 创建空白占位元素
   * @param height 高度（mm）
   * @returns 空白元素
   */
  static createEmptyElement(height: number): HTMLElement {
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'custom-div empty';
    emptyDiv.style.height = height + 'mm';
    return emptyDiv;
  }

  /**
   * 创建分页线元素
   * @param pageNum 页码
   * @returns 分页线元素
   */
  static createPageLine(pageNum: number): HTMLElement {
    const pageLine = document.createElement('div');
    pageLine.style.top = pageHeight * pageNum + 'mm';
    pageLine.className = 'custom-div page-line';
    return pageLine;
  }

  /**
   * 计算题目内容高度
   * @param element 题目容器元素
   * @returns 题目内容高度（mm）
   */
  static getQuesContentHeight(element: HTMLElement | null): number {
    if (!element) return 0;
    
    let quesHeight = 0;
    Array.from(element.children).forEach(child => {
      const htmlChild = child as HTMLElement;
      if (!htmlChild.classList.contains('left')) {
        quesHeight += htmlChild.offsetHeight;
      }
    });
    
    return pxConversionMm(quesHeight);
  }

  /**
   * 设置元素页面属性
   * @param element DOM元素
   * @param pageSize 页码
   * @param layout 布局类型
   */
  static setElementPageAttributes(
    element: HTMLElement, 
    pageSize: number, 
    layout: IPAGELAYOUT
  ): void {
    element.setAttribute('page', pageSize.toString());
    
    // 设置元素宽度
    const width = LayoutUtils.getPageWidth(layout, pageSize);
    if (width) {
      element.style.width = width;
    } else {
      element.style.width = '';
    }
  }
}
