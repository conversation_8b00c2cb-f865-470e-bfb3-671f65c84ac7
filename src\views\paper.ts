import Observe from "@/plugins/Observe";
import { getSubject } from "@/service/api";
import { getBrowerZoom } from "@/utils/util";
import {
  DECIMAL_TYPE,
  GRIDPLACE_TYPE,
  GRID_TYPE,
  IBigQues,
  ICARDMODEL,
  ICorrectType,
  IEditerPowerType,
  IPAGELAYOUT,
  ISubject,
  JUDGE_TYPE,
  MARK_TYPE,
  ARRANGE_TYPE,
  LINE_WIDTH,
  QUES_TYPE,
  QUES_SCAN_MODE,
  INUMBERLAYOUT,
  ISmallQues,
  IRULERTYPE,
  LineModel,
  ANSWER_TYPE,
} from "@/typings/card";
import {
  arabToChinese,
  deepClone,
  generateUUID,
  getQueryString,
} from "@/utils/util";
import { PaperConstant } from "./paper.constant";
import { ElMessage } from "element-plus";

export class PaperManager extends Observe {
  // 实例成员
  private static instance: PaperManager;
  public static GetInstance() {
    return PaperManager.instance;
  }

  //试卷名称
  public name = "";
  public nameHtml = {
    html: "",
    size: "4.8mm",
    fontBold: true,
  };
  // 是否模板
  public isTemplate: 0 | 1 = 1
  //试卷号
  public paperId = "";
  //试卷id
  public tbId = "";
  //用户id
  public userId = "";
  //学校id
  public schoolId = "";
  //学科考号
  public stuNoLength = 0;
  //学校考号列表
  public stuNoList = [];
  //试卷批阅类型
  public correctType: ICorrectType = ICorrectType.HAND;
  //基础学科列表
  public subjectList: Array<ISubject> = [];
  //选择的学科
  public subjectId = "";
  // 多学科列表
  get multiSubjects() {
    let subIds = this.subjectId.split(',');
    if (subIds.length <= 1) return [];

    let list = [];
    this.subjectList.forEach(sub => {
      if (!subIds.includes(sub.subjectId)) return;

      list.push(sub);
    })
    return list;
  }
  //年级
  public gradeId = "";
  //页面布局
  public pageLayout: IPAGELAYOUT =
    Number(getQueryString("pageLayout")) || IPAGELAYOUT.A4;
  // 考号类型
  public numberLayout: INUMBERLAYOUT = INUMBERLAYOUT.TICKET;
  //每页有栏 A4 一栏 A3 两栏
  public column: IPAGELAYOUT = 1;
  //制卡模式
  public cardType: ICARDMODEL = ICARDMODEL.ONLYCARD;
  //字号
  public fontSize = "3.7mm";
  //行距
  public space = "7mm";
  //字体
  public fontFamily = `'新罗马',"Times New Roman",'宋体',SimSun-ExtB,SimSun,NSimSun,serif,'STSong'`;
  // 标题字体
  public titleFontFamily = `'新罗马',"Times New Roman",'宋体',SimSun-ExtB,SimSun,NSimSun,serif,'STSong'`;
  //简答题下划线标识样式
  public hasLine = false;
  //是否编辑进入，屏蔽操作
  public isEdit = false;
  //是否关联考试，屏蔽操作
  public isRelatedWork = false;
  //选项个数默认4个
  public optionCount = 4;
  //选项个数最多26个
  public optionCountMax = 26;
  //步进默认1
  public step = 1;
  //总页数
  public pageCount = 1;
  //判断题默认样式默认TF
  public judgeType: JUDGE_TYPE = this.isEnglishSubject() ? JUDGE_TYPE.TF : JUDGE_TYPE.MARK;
  //打分框模式默认分数
  public markType: MARK_TYPE = MARK_TYPE.NUMBER;
  //填空题判分格位置默认题前,简答题默认题前且不支持设置
  public gridPlace: GRIDPLACE_TYPE = GRIDPLACE_TYPE.FRONT;
  //十个位分开默认不分开
  public gridType: GRID_TYPE = GRID_TYPE.NO;
  //分数精度默认有小数点
  public decimal: DECIMAL_TYPE = DECIMAL_TYPE.NOTHAVE;
  //简答题默认行数
  public rowNum = 4;
  //试卷总分
  public fullScore = 100;
  //题目结构集合
  public quesInfos: Array<IBigQues> = [];
  //在卡片中呈现的题目
  public quesInfosInCard: Array<IBigQues> = [];
  // 题卡合一题目临时缓存
  private quesCardInfosTemp: Array<IBigQues> = [];
  // 题卡合一 紧凑型图文排版数据
  public groupImgData: Map<string, any> = new Map();
  //源题目信息
  public questions: Array<any> = [];
  public items: Array<any> = [];
  //题目排列方式
  public arrange: ARRANGE_TYPE = ARRANGE_TYPE.VERTICAL;
  //填空题空格长度
  public lineType: LINE_WIDTH = LINE_WIDTH.THIRD;
  //题目允许跨页
  public spreadQues = 1;
  // 题目允许合并
  public mergeQues = 0;
  //答题区前置
  public ansToTop = 0;
  //作答前置配置
  public ansToTopConfig = {
    object: false,
    fill: false,
  }
  //客观题作答类型
  public ansType = ANSWER_TYPE.fill;
  //页眉名称
  public headerName = "";
  //是否为作文题
  public isWriting = false;
  //作文字数
  public wordNumber = 800;
  //作文格大小
  public cellSize = "medium";
  public cells = 23;
  //填空题空格数
  public lineNum = 1;
  //答题卡选项字号
  public optionFontSize = "2.6mm";
  // 填空题是否可以智能批改
  public isCanFillEva = false;
  //是否启用密封线
  public isSealLine = false;
  //是否启用智批改
  public isAICorrect = false;
  //是否启用手写
  public isHandWrite = false;
  //是否开启客观题手写作答
  public isHandWriteAns = false;
  //是否启用填空题线上批改
  public isScanFill = false;
  //考号是否包含X
  public isNumberHasX = false;
  //图文排版
  public pageType = 0;
  //是否支持多位考号头部
  public isMoreNoHeader = false;

  // pdf页码
  private paperNum: number = 0;
  // 小题序号
  private smallQuesNo: number = 1;
  //编辑权限类型
  public editerType = IEditerPowerType.NORMAL;
  //当前卡片是否保存过数据
  public isSaveData = false;
  //ab卡类型
  public abType = '';
  //是否启用AB卷
  public isABPaper = false;
  //智批学科
  public aiScanSubjectIds = [];

  constructor() {
    super();

    PaperManager.instance = this;
    const correctType =
      getQueryString("correctType")
        ? Number(getQueryString("correctType")) : ICorrectType.HAND;

    this.setCorrectType(correctType)
  }

  public setCorrectType(type: ICorrectType): ICorrectType {
    this.correctType = type;
    if (type === ICorrectType.PHOTO) {
      this.optionCountMax = 8;
      this.cardType = ICARDMODEL.PHOTO;
    } else {
      this.optionCountMax = 26;
    }

    return type;
  }

  /**
   * @name 是否纯客观题的单选题
   **/
  public isMixinAllObjectQues(item: IBigQues): boolean {
    if (!item.data.length) return false;

    return item.data.every(subItem => this.isObjectiveQues(subItem.typeId));
  }

  /**
   * @name 是否选择题，包含单选和多选
   **/
  public isChoiceQues(typeId: QUES_TYPE): boolean {
    return typeId == QUES_TYPE.choice || typeId == QUES_TYPE.singleChoice;
  }

  /**
   * @name 是否填空题题，包含智批
   **/
  public isFillQues(typeId: QUES_TYPE): boolean {
    return typeId == QUES_TYPE.fill || typeId == QUES_TYPE.fillEva;
  }

  /**
   * @name 是否客观题，包含单选和多选，判断题
   **/
  public isObjectiveQues(typeId: QUES_TYPE): boolean {
    return this.isChoiceQues(typeId) || typeId == QUES_TYPE.judge;
  }

  /**
   * @description: 切换到拍改模式
   * @return {*}
   */
  public switch2PhtotMode() {
    this.pageLayout = IPAGELAYOUT.A4;
    if (this.cardType == ICARDMODEL.QUESCARD) {
      this.cardType = ICARDMODEL.ONLYCARD;
    }
  }

  /**
   * @name 检测是否混合题，如果小题的题型和答题不完全一致则为混合题
   **/
  public checkQuesIsMixin(ques: IBigQues): boolean {
    if (!ques.data) return false;

    return !ques.data.every(item => {
      // 选择题支持合并，需要特定判断
      if (this.isChoiceQues(ques.typeId)) {
        return this.isChoiceQues(item.typeId)
      }

      // 填空题和智批题需要单独判断
      if (this.isFillQues(ques.typeId)) {
        return this.isFillQues(item.typeId)
      }

      return item.typeId == ques.typeId;
    })
  }

  /**
   * @name 获取总分值
   **/
  public getTotalScore(): number {
    let score = 0;
    this.quesInfos.forEach((item) => {
      if (item.parentId) return;
      score += Number(item.score * 10);
    });

    return score / 10;
  }

  /**
   * @name 计算大题分值
   **/
  public caculBigQuesScore(item: IBigQues) {
    let score = 0;
    let firstScore = 0;
    let isAverage = true;

    item.data.forEach((subItem: any, index: number) => {
      if (index === 0) firstScore = subItem.score;
      if (subItem.mixinMode) return;
      if (subItem.isChooseDo && !subItem?.targetIds?.includes(subItem.id)) return;

      score += Number(subItem.score * 10);
      if (subItem.score !== firstScore) isAverage = false;
    });

    item.score = score / 10;
    if (!isAverage) item.quesScore = "";
  }


  /**
   * @description: 
   * @param {string} width
   * @param {number} lineIndex
   * @return {*}
   */
  public createLineModel(width: string, lineIndex: number, oldLine?: LineModel): LineModel {
    return {
      index: lineIndex,
      width,
      editContent: oldLine && oldLine.editContent || '',
      isLineFeed: oldLine && oldLine.isLineFeed || false,
      id: oldLine && oldLine.id || generateUUID(),
      score: oldLine && oldLine.score || ''
    };
  }

  /**
   * @description: 更新填空题线条列表
   * @param {ISmallQues} ques
   * @param {number} pageWidth
   * @return {ISmallQues}
   */
  public updateFillLineList(ques: ISmallQues, pageWidth: number): ISmallQues {
    // 横线的间隔区域，10
    const OFFSETX = 10 * (ques.lineType + 1);
    ques.lineWidth = (pageWidth - OFFSETX) / ques.lineType + 'mm';

    if (ques.data) {
      // 复杂题型，三级小题
      let _ques = ques.data[0];
      if (!_ques.lineList) _ques.lineList = [];
      _ques = this.updateFillLineList(_ques, pageWidth);
    }

    if (!ques.lineList) ques.lineList = [];
    let cacheLineLength = ques.lineList.length;
    if (cacheLineLength > 0) {
      // 计算线条数量差异，补充不足线条
      let lengthDiffer = ques.lineNum - cacheLineLength;
      if (lengthDiffer !== 0) {
        if (lengthDiffer < 0) {
          ques.lineList = ques.lineList.slice(0, ques.lineNum)
        } else {
          for (let i = 0; i < lengthDiffer; i++) {
            ques.lineList.push(this.createLineModel(ques.lineWidth, i));
          }
        }
      }

      // 更新线条保留线条上的文字
      ques.lineList = ques.lineList.map((line: LineModel, index: number) => {
        if (ques.lineWidth === line.width || this.cardType == ICARDMODEL.QUESCARD) return line;
        return this.createLineModel(ques.lineWidth, index, line)
      })
    } else {
      for (let i = 0; i < ques.lineNum; i++) {
        let _line = this.createLineModel(ques.lineWidth, i)
        //如果空数为1，设置空分值为题分值
        if (ques.lineNum == 1) {
          _line.score = ques.score;
        }
        ques.lineList.push(_line);
      }
    }

    return ques;
  }

  /**
   * @name 更新题目数据
   **/
  public updateQuestions(arg1: number, arg2: IBigQues): Array<IBigQues>;
  public updateQuestions(
    arg1: number,
    arg2: string,
    arg3: any
  ): Array<IBigQues>;
  public updateQuestions(arg1: Array<IBigQues>): Array<IBigQues>;
  public updateQuestions(
    arg1: number | Array<IBigQues>,
    arg2?: IBigQues | string,
    arg3?: any
  ): Array<IBigQues> {
    if (typeof arg1 === "number") {
      if (typeof arg2 === "string") {
        this.quesInfos[arg1][arg2] = arg3;
      } else {
        this.quesInfos[arg1] = arg2;
      }
    } else {
      this.quesInfos = arg1;
    }

    return this.quesInfos;
  }

  /**
   * @name 更新并且合并题目数据
   **/
  public updateAndMergeQuestions(arg1: number, arg2: IBigQues): Array<IBigQues>;
  public updateAndMergeQuestions(
    arg1: number,
    arg2: string,
    arg3: any
  ): Array<IBigQues>;
  public updateAndMergeQuestions(arg1: Array<IBigQues>): Array<IBigQues>;
  public updateAndMergeQuestions(
    arg1: number | Array<IBigQues>,
    arg2?: IBigQues | string,
    arg3?: any
  ): Array<IBigQues> {
    if (typeof arg1 === "number") {
      if (typeof arg2 === "string") {
        this.quesInfos[arg1][arg2] = arg3;
      } else {
        this.quesInfos[arg1] = arg2;
      }
    } else {
      this.quesInfos = arg1;
    }

    if (this.cardType == ICARDMODEL.QUESCARD || this.mergeQues === 0) {
      this.updateQuesInfosInCard([]);
      this.updateQuesInfosInCard(this.quesInfos);
      return this.quesInfos;
    } else {
      return this.mergeObjectiveQuestions();
    }
  }

  /**
   * @name 更新卡片的数据格式
   */
  public updateQuesInfosInCard(list: Array<IBigQues>) {
    if (list.length) {
      if (this.cardType === ICARDMODEL.ONLYCARD) {
        this.quesCard2onlyCard(list.slice());
      } else {
        this.quesInfosInCard = list.slice();
      }
    } else {
      this.quesInfosInCard = [];
    }
  }

  /**
   * @name 获取题目数据
   */
  public getQuesInfos() {
    return this.quesInfos;
  }

  /**
   * @name 切换页面布局
   */
  public changePageLayout(val: IPAGELAYOUT) {
    this.pageLayout = val;
    this.trigger("changeLayout", val);
  }

  /**
   * @description: 切换考号类型
   */
  public changeNumberLayout(val: INUMBERLAYOUT) {
    this.numberLayout = val;
    this.trigger("changeNumberLayout", val);
  }

  /**
   * @name 合并客观题
   * @returns Array<IBigQues>
   **/
  public mergeObjectiveQuestions(): Array<IBigQues> {
    this.mergeQues = 1;
    if (!this.quesInfos.length) {
      this.updateQuesInfosInCard([]);
      return [];
    }

    // 选择题列表
    let choiceQues = [];
    // 选择题队列索引位置
    let choiceQueueIndex = -1;
    let choiceQuesFlat = [];
    // 拎出选择题
    let mergedQuestions = this.quesInfos.filter((item, index) => {
      if (item.parentId && !PaperConstant.ALLOW_MIXINMERGE) return true;

      // 仅答题卡模式检测是否混合题
      if (this.cardType == ICARDMODEL.ONLYCARD) {
        let isMixin = this.checkQuesIsMixin(item);
        if (isMixin) {
          if (PaperConstant.ALLOW_MIXINMERGE) {
            // 从混合题中拎出选择题
            item.data.filter(subItem => {
              // if (!this.isChoiceQues(subItem.typeId)) return true;
              if (!this.isObjectiveQues(subItem.typeId)) return true;

              subItem.name = String(subItem.name);
              let cItem = deepClone(subItem)
              if (!cItem.data) {
                cItem.data = [subItem];
              }
              if (choiceQueueIndex === -1) choiceQueueIndex = 0;
              choiceQues.push(cItem);
            })
          }

          return true;
        }
      }

      // 排除全客观题题的混合题
      if (item.typeId == QUES_TYPE.mixin && this.isMixinAllObjectQues(item)) return false;
      // 是否客观题
      if (!this.isObjectiveQues(item.typeId)) return true;

      if (choiceQueueIndex === -1) choiceQueueIndex = 0;
      // 将独立成组的数据带给合并组
      item.data[0].groupAlone = item.groupAlone;

      choiceQues.push(item);
      return false;
    });

    if (!choiceQues.length) {
      this.updateQuesInfosInCard(mergedQuestions);
      this.trigger("switchMergeQues");
      return mergedQuestions;
    }

    if (choiceQues.length === 1) {
      choiceQues[0].showName = 1;
      choiceQues[0].name =
        choiceQues[0].name.substring(
          0,
          choiceQues[0].name.indexOf("、") + 1
        ) + "客观题";
      mergedQuestions.splice(choiceQueueIndex, 0, ...choiceQues);
    } else {
      /** 不止一道选择题,将题目合并到一起:start */
      // 扁平选择题列表，综合单选多选判断题
      let compreChoiceQues = deepClone(choiceQues[0]);
      compreChoiceQues.id = generateUUID();
      compreChoiceQues.score = 0;
      compreChoiceQues.isMergedObject = true;
      compreChoiceQues.data = [];
      compreChoiceQues.name =
        compreChoiceQues.name.substring(
          0,
          compreChoiceQues.name.indexOf("、") + 1
        ) + "客观题";
      // 父层题型统一设置为选择题
      compreChoiceQues.typeId = QUES_TYPE.choice;

      choiceQues.forEach((item, index) => {
        if (index === 0) {
          // 取首个选择大题的showType标记属性
          compreChoiceQues.showName = 1;
          compreChoiceQues.showType = item.showType || 'vertical';
          compreChoiceQues.verticalLines = item.verticalLines || 5;
        } else {
          // 后续的填空题复制showType
          item.showType = compreChoiceQues.showType;
          item.verticalLines = compreChoiceQues.verticalLines;
        }
        choiceQuesFlat = choiceQuesFlat.concat(item.data);

        if (item.count === undefined) item.count = item.data.length;
        compreChoiceQues.score += Number(item.score);
      });
      compreChoiceQues.data = choiceQuesFlat;
      compreChoiceQues.count = choiceQuesFlat.length;
      mergedQuestions.splice(choiceQueueIndex, 0, compreChoiceQues);
      /** 不止一道选择题,将题目合并到一起:end */
    }
    this.updateQuesInfosInCard(mergedQuestions);
    this.trigger("switchMergeQues");
    return this.quesInfosInCard;
  }

  /**
   * @name 拆分客观题
   * @returns Array<IBigQues>
   **/
  public splitObjectiveQuestions(): Array<IBigQues> {
    this.mergeQues = 0;
    if (!this.quesInfos.length) {
      this.updateQuesInfosInCard([]);
      return [];
    }

    this.updateQuesInfosInCard([]);
    this.updateQuesInfosInCard(this.quesInfos);
    // 去除首个单选大题的fullname标题
    // for (const ques of this.quesInfos) {
    //   if (ques.typeId == QUES_TYPE.choice || ques.typeId == QUES_TYPE.singleChoice) {
    //     delete ques.fullname;
    //     break;
    //   }
    // }
    this.trigger("switchMergeQues");
    return this.quesInfos;
  }

  /**
   * @name 合并主观题
   * @param qId 大题id
   * @param qIndex 小题索引
   * @returns {Array<IBigQues>}
   **/
  public mergeSubjectiveQuestions(
    qId: string,
    qIndex: number
  ): Array<IBigQues> {
    let ques = this.quesInfos.find((item) => item.id === qId);
    ques.data[qIndex].isSplited = false;

    return this.checkOMergebjective();
  }

  /**
   * @name 清除fullname字段
   **/
  private clearFullname() {
    this.quesInfos.forEach(item => {
      if (item.fullname) delete item.fullname;
    })
  }

  /**
   * @name 仅答题卡题卡合一，数据转换
   **/
  public onlyCard2quesCard() {
    if (!this.quesCardInfosTemp.length) {
      this.clearFullname()
      return;
    }

    this.quesInfos = deepClone(this.quesCardInfosTemp);
    this.clearFullname();
  }

  /**
   * @name 题卡合一转仅答题卡，数据转换
   **/
  public quesCard2onlyCard(
    quesInfos: Array<IBigQues>,
    backup: boolean = false
  ) {
    if (backup) {
      this.quesCardInfosTemp = deepClone(quesInfos);
    }
    this.quesInfosInCard = quesInfos.slice();

    let addedCount = 0;
    quesInfos.forEach((ques: any, index) => {
      if (ques.typeId == QUES_TYPE.mixin) {
        addedCount++;
        return;
      }

      let isMixin = this.checkQuesIsMixin(ques);
      // 非混合题或合并的混合题不参与拆分处理
      if (!isMixin || ques.isMergedObject) {
        addedCount++;
        return;
      }

      // 转换题卡中的混合题
      let mixinQuesGroup = [];
      let mixinNameQues = deepClone(ques);

      mixinNameQues.typeId = QUES_TYPE.mixin;
      mixinNameQues.mixinMode = true;
      mixinQuesGroup.push(mixinNameQues);

      for (let i = 0; i < mixinNameQues.data.length; i++) {
        let subQues = mixinNameQues.data[i];
        subQues.parentId = ques.id;
        subQues.quesName = subQues.quesNos;
        let smallQues = deepClone(subQues);
        smallQues.parentId = subQues.id;
        subQues.data = [smallQues];
        mixinQuesGroup.push(subQues);
      }
      mixinNameQues.data = [];

      this.quesInfosInCard.splice(addedCount, 1, ...mixinQuesGroup);
      addedCount += mixinQuesGroup.length;
    });
  }

  /**
   * @name 拆分主观题
   * @returns {Array<IBigQues>}
   **/
  public splitSubjectiveQuestions(
    qId: string,
    height?: string
  ): Array<IBigQues> {
    let ques = this.quesInfos.find((item) => {
      if (item.id === qId) return true;
      if (!item.data || item.typeId == QUES_TYPE.mixin) return false;

      return item.data.some(subItem => {
        return subItem.id === qId
      });
    });
    ques.data.forEach((item: any) => {
      item.isSplited = true;
    });

    return this.checkOMergebjective();
  }

  /**
   * @name 检查合并客观题
   * @returns {Array<IBigQues>}
   **/
  public checkOMergebjective(): Array<IBigQues> {
    if (this.mergeQues === 1) {
      return this.mergeObjectiveQuestions();
    } else {
      return this.quesInfosInCard;
    }
  }


  /**
   * @description: 改变智批题改为默认填空题
   * @return {*}
   */
  public changeAllFillEvaDefault() {
    for (const item of this.quesInfos) {
      if (item.typeId == QUES_TYPE.fillEva) {
        item.typeId = QUES_TYPE.fill;
      };

      item.data && item.data.forEach(subItem => {
        if (subItem.typeId == QUES_TYPE.fillEva) subItem.typeId = QUES_TYPE.fill
        if (subItem.data && subItem.data.length) subItem.data.forEach(ssit => {
          if (ssit.typeId == QUES_TYPE.fillEva) ssit.typeId = QUES_TYPE.fill
        })
      });
    }

    return this.quesInfos;
  }

  /**
   * @name 插入/替换题目数据
   **/
  public spliceQuestions(index: number, howmany: number, datas?: IBigQues[], mode?: '' | 'AB') {
    this.quesInfos.splice(index, howmany, ...(datas ? datas : []));
    if (mode != 'AB') {
      const data = {
        from: this.abType,
        type: 'updateQuestions',
        func: 'spliceQuestions',
        arguments: [index, howmany, datas],
      }
      window.parent.postMessage(JSON.stringify(data), '*');
    }
  }

  /**
   * @name 通过题目id获取在源数据中的小题
   * @param {String} id
   */
  public findSmallQuesByQuesId(id: string): ISmallQues {
    for (const bigQues of this.quesInfos) {
      if (!bigQues.data) continue;

      for (const smallQues of bigQues.data) {
        if (smallQues.id === id) return smallQues;
      }
    }
  }
  /**
  * @name 通过小题id获取在源数据中的大题
  * @param {String} id
  */
  public findBigQuesBySmallQuesId(id: string): IBigQues {
    if (!id) return;
    for (const bigQues of this.quesInfos) {
      if (!bigQues.data) continue;

      for (const smallQues of bigQues.data) {
        if (smallQues.id === id) return bigQues;
      }
    }
  }

  /**
   * @name 通过题目id获取在源数据中的大题索引
   * @param {String} id
   */
  public findBigIndexByQuesId(id: string): number {
    return this.quesInfos.findIndex((item) => item.id === id);
  }

  /**
   * @name 通过题目id获取在源数据中的大题
   * @param {String} id
   */
  public findBigQuesByQuesId(id: string): IBigQues {
    return this.quesInfos.find((item) => item.id === id);
  }

  /**
   * @name 通过小题id获取在源数据中的混合小题组
   * @param {String} id
   */
  public findMixinQuesBySmallId(id: string): ISmallQues[] {
    let smallQuesList = [];
    for (const bigQues of this.quesInfos) {
      if (!bigQues.mixinMode || !bigQues.data) continue;

      for (const smallQues of bigQues.data) {
        if (!smallQues.parentId || smallQues.parentId !== id) continue;

        smallQuesList.push(smallQues)
      }
    }

    return smallQuesList;
  }

  /**
   * @name 通过题目id删除源数据中的大题
   * @param {String} id
   */
  public removeBigQuesByQuesId(id: string): IBigQues[] {
    let qIndex = this.findBigIndexByQuesId(id);

    this.spliceQuestions(qIndex, 1);

    return this.quesInfos;
  }

  /**
   * @name 通过typeId获取在源数据中的大题
   * @param {String} typeIds
   */
  public findBigQuesByTypeId(typeIds: QUES_TYPE[]): IBigQues {
    return this.quesInfos.find((item) => typeIds.includes(Number(item.typeId)));
  }

  /**
   * @name 获取大题数量
   */
  public getBigQuesLength(): number {
    let len = 0;
    this.quesInfos.forEach((item) => {
      // 跳过混合题小题的统计
      if (item.parentId) return;
      len++;
    });

    return len;
  }

  /**
   * @name 获取小题数量，不包含小问数
   */
  public getSmallQuesLength(): number {
    let len = 0;
    this.quesInfos.forEach((item) => {
      if (item.parentId) {
        len++;

        return;
      }

      if (item.typeId == QUES_TYPE.mixin) return;

      if (item.mixinMode) {
        item.data.forEach((subItem: any) => {
          if (subItem.parentId) return;

          len++;
        });
      } else {
        len += item.data.length;
      }
    });

    return len;
  }

  /**
   * @name: 获取基础学科
   */
  public async getSubject(schoolId: string): Promise<void> {
    let res = await getSubject({
      schoolId: schoolId,
    });

    if (res && res.code === 1) {
      this.subjectList = [];
      res.data.forEach((item) => {
        this.subjectList.push({
          subjectId: item.id,
          subjectName: item.subject_name,
        });
      });
      this.setHasLineBySubject(getQueryString("subjectId") || res.data[0].id);
      this.setIsCanFillEvaBySubject(
        getQueryString("subjectId") || res.data[0].id
      );
    }
  }

  /**
   * @name: 通过学科设置是否显示横线
   */
  public setHasLineBySubject(subjectId: string) {
    if (subjectId === "") return;

    this.hasLine = [24, 25, 1, 10, 3, 12].includes(Number(subjectId));
  }

  /**
   * @description: 通过学科设置是否显示智能批改
   */
  public setIsCanFillEvaBySubject(subjectId: string) {
    if (subjectId === "") return;

    this.isCanFillEva = this.aiScanSubjectIds.includes(Number(subjectId));
  }
  /**
   * @description: 获取是否可以智批
   */
  public getIsCanFillEva(typeId: QUES_TYPE): boolean {
    return this.isCanFillEva && this.isFillQues(typeId)
  }

  /**
   * @description: 是否英语学科
   */
  public isEnglishSubject() {
    return [25, 3, 12].includes(Number(this.subjectId));
  }
  /**
   * @description: 是否语文学科
   */
  public isChineseSubject() {
    return [1, 10, 24].includes(Number(this.subjectId));
  }
  /**
   * @description: 是否理科、数理化
   */
  public isMathSubject() {
    return [11, 13, 14, 2, 4, 5].includes(Number(this.subjectId));
  }

  /**
   * @name 通知局部数据更新
   * @param {string} from  来源
   */
  public notifyUpdateData(from: 'card' | 'right') {
    this.trigger("updateQustions", from);
  }

  /**
   * @name 通知全部数据更新
   * @param {string} from  来源
   */
  public notifyUpdateAllData() {
    this.trigger("updateAllQustions");
  }

  /**
   * @name 获取temp页面html
   */
  getTempHtml() {
    if (this.cardType == ICARDMODEL.BLANKCARD) return "";

    let qObj: any = {};
    for (const key in this.questions) {
      const doms = document.querySelectorAll(`[id="${key}"]`);
      let qhtml = "";
      let thtml = "";
      let opt: any[] = [];
      const value = JSON.parse(JSON.stringify(this.questions[key]));
      Array.from(doms).forEach((dom) => {
        if (!dom) return;
        const _dom: any = dom
          .getElementsByClassName("ques-box")[0]
          ?.cloneNode(true);
        if (!_dom) return;
        // 先删除只包含br标签且className含有13位数字的空div 避免操作过程中出现大量空标签导致页面卡死
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = _dom.innerHTML;
        const emptyDivs = tempDiv.querySelectorAll('div');
        const targetDivs = Array.from(emptyDivs).filter(div => 
          div.className && /\b\d{13}\b/.test(div.className) && 
          ((div.children.length === 1 && 
          div.children[0].tagName.toLowerCase() === 'br') || div.children.length == 0)
        );
        if (targetDivs.length > 30) {
          ElMessage.warning("题目数据存在异常，本次保存会自动删除异常内容，请保存成功后刷新页面查看数据是否正常!");
          targetDivs.forEach(div => div.remove());
        }
        _dom.innerHTML = tempDiv.innerHTML.replace(/remove-ques/g, "");
        const deletDom = [
          // "noeditsave",
          "cke_widget_drag_handler_container",
          "cke_image_resizer",
          "hand-write-div",
        ];
        deletDom.forEach((el) => {
          Array.from((_dom as HTMLElement)?.getElementsByClassName(el)).forEach(
            (item: any) => {
              //获取自身节点的父节点
              const parent = item.parentElement;
              //删除节点
              parent.removeChild(item);
            }
          );
        });
        Array.from(_dom.getElementsByClassName("ck-math-tex")).forEach(
          (item: any) => {
            let span = document.createElement("span");
            span.className = 'ck-math-tex';
            span.innerHTML = item.getAttribute("tex");
            span.setAttribute("tex",item.getAttribute("tex"));
            item.parentElement.replaceChild(span, item);
          }
        );
        thtml += _dom.innerHTML;
      });
      value.editContent = thtml;
      value.qsList[0].editContent = thtml;
      qObj[key] = value;
    }
    const qhtml = JSON.stringify(qObj);
    return qhtml;
  }

  /**
   * @name 获取页面html
   */
  private getSaveHtml() {
    const temp = document
      .getElementsByClassName("page-box")[0]
      .cloneNode(true) as Element;
    const deletDom = [
      "hand-write-div",
      "nosave",
      "cke_widget_drag_handler_container",
      "cke_image_resizer",
    ];
    deletDom.forEach((el) => {
      Array.from(temp.getElementsByClassName(el)).forEach((item: any) => {
        //获取自身节点的父节点
        const parent = item.parentElement;
        //删除节点
        parent.removeChild(item);
      });
    });
    Array.from(temp.getElementsByClassName("ck-math-tex")).forEach(
      (item: any) => {
        let span = document.createElement("span");
        span.innerHTML = item.getAttribute("tex");
        span.setAttribute("tex",item.getAttribute("tex"));
        span.className = 'ck-math-tex';
        // item = span
        item.parentElement.replaceChild(span, item);
      }
    );
    return temp.innerHTML;
  }

  /**
   * @name:标记小题序号
   */
  private markSmallQuseNo(quesItem: any, qIndex: number) {
    if (!quesItem.data || !quesItem.data.length) {
      quesItem.quesNo = this.smallQuesNo++;

      // 如果需要被置空不需要再重新赋值
      if (quesItem.quesName === '' || quesItem.quesName === ' ') return;

      // 获取修改的小题标题
      let quesNosEle: NodeListOf<HTMLElement> = document.querySelectorAll(`[qid="${quesItem.id}"] .ques-sort`);
      if (quesNosEle.length) {
        qIndex = quesNosEle.length === 1 ? 0 : qIndex;
        if (qIndex > quesNosEle.length - 1) return

        quesItem.quesName = quesNosEle[0].innerText;
      }
      return;
    }

    quesItem.data.forEach((qItem: any, index: number) =>
      this.markSmallQuseNo(qItem, index)
    );
  }

  /**
   * @name:保存自定义标题
   */
  private saveCustionQuesName(ques: IBigQues) {
    let nameEle = null;
    let $qDom = null;

    $qDom = document.getElementById(ques.id);
    // 仅答题卡并且合并模式
    if (this.cardType == ICARDMODEL.ONLYCARD && this.mergeQues) {
      let isMixin = this.checkQuesIsMixin(ques);
      if (!$qDom && ques.data) {
        $qDom = document.getElementById(ques.data[0].id);
      }
      if (!$qDom) return;

      if (ques.data) {
        let firstQues: any = ques.data[0];
        if (isMixin && this.isChoiceQues(firstQues.typeId)) {
          // 如果混合题的第一题为选择题，则单独设置标题
          this.saveCustionQuesName(firstQues)
        }
      }

    } else if (!$qDom) {
      if (ques.typeId != QUES_TYPE.subject) {
        // 混合模式题型
        if (!$qDom) {
          $qDom = document.getElementById(ques.data[0].id);
        }
        if (!$qDom) return;
      } else {
        $qDom = document.getElementById(ques.data[0].id);
        if (!$qDom) return;
      }
    }

    nameEle = $qDom.querySelectorAll(".ques-content-name>.ques-name-wrap");
    if (!nameEle.length) {
      nameEle = $qDom.querySelectorAll(".ques-content-name");
    }
    if (!nameEle.length) return;

    ques.fullname = nameEle[0].innerHTML;
  }

  /**
   * @name:格式化题卡合一的混合题
   * 根据maxLevel确定2/3级题型
   */
  private formatQUESCARDMixinQues() {
    if (this.cardType === ICARDMODEL.BLANKCARD) return;

    this.quesInfos.forEach(qItem => {
      let isMixin = this.checkQuesIsMixin(qItem);
      if (!isMixin) return;

      qItem.data.forEach((subQItem: any) => {
        if (subQItem.maxLevel === 2) {
          delete subQItem.data;
        }
        delete subQItem.maxLevel;
      })
    })
  }

  /**
   * @name:获取保存答题卡内容
   */
  public getSaveCardContent() {
    this.formatQUESCARDMixinQues();
    if (this.cardType === ICARDMODEL.QUESCARD) return this.quesInfos;

    // 仅答题卡模式需要重新序列化
    const quesInfos = deepClone(this.quesInfos);
    let outputList = [];
    // console.log('outputList:1', quesInfos);

    quesInfos.forEach((item: any, index: any) => {
      if (item.parentId) return;

      if (item.mixinMode) {
        // 混合模式的题型
        if (item.typeId == QUES_TYPE.mixin) {
          // 混合题型的答题
          let parentId = item.id;
          // 将子集题目收集在父层内
          item.data = quesInfos.filter((subItem) => {
            if (parentId !== subItem.parentId) return false;

            if (subItem.typeId == QUES_TYPE.subject) {
              subItem.data.forEach(this.addSubjectHtmlContent);
            } else if (this.isFillQues(subItem.typeId)) {
              // 填空题保存对应的html内容（自定义图片）
              let $dom = $(`#${subItem.id} .imglist-content`);
              subItem.editContent = $dom.html();
            }

            return true;
          });


          outputList.push(item);
        } else {
          // 单题型的混合模式
          let singleMixin = deepClone(item);
          singleMixin.data = [];
          if (singleMixin.children) delete singleMixin.children;

          item.data.forEach((subItem) => {
            if (subItem.parentId) return;

            if (subItem.mixinMode) {
              let _mixin = deepClone(subItem);
              if (_mixin.children) delete _mixin.children;
              let parentId = subItem.id;
              // 将子集题目收集在父层内
              _mixin.data = item.data.filter((ssubItem) => {
                if (parentId !== ssubItem.parentId) return false;

                if (!this.isObjectiveQues(ssubItem.typeId)) {
                  this.addSubjectHtmlContent(ssubItem);
                }
                if (ssubItem.children) delete ssubItem.children;

                return true;
              });

              singleMixin.data.push(_mixin);
            } else {
              if (subItem.data) {
                if (this.isFillQues(subItem.typeId)) {
                  subItem.lineList.forEach((line, index) => {
                    let subLine = subItem.data[0].lineList[index];
                    if (index > 0 && !subLine) subLine = subItem.data[0].lineList[0];

                    line.score = subLine.score;
                  });
                }
                delete subItem.data;
              }
              if (subItem.typeId == QUES_TYPE.subject) {
                this.addSubjectHtmlContent(subItem);
              }
              singleMixin.data.push(subItem);
            }
          });

          // 填空题保存对应的html内容（自定义图片）
          if (this.isFillQues(singleMixin.typeId)) {
            let $dom = $(`#${singleMixin.id} .imglist-content`);
            singleMixin.editContent = $dom.html();
          }

          outputList.push(singleMixin);
        }
      } else {
        item.data.forEach((subItem) => {
          if (subItem.children) delete subItem.children;

          if (subItem.typeId == QUES_TYPE.subject) {
            // check：如果data只有一个数据，说明当前为一级题，清除data字段
            if (this.cardType === ICARDMODEL.BLANKCARD && subItem.data && subItem.data.length === 1) {
              delete subItem.data;
            }

            if (subItem.lineList) delete subItem.lineList;
            this.addSubjectHtmlContent(subItem);
          }
        });

        // 填空题保存对应的html内容（自定义图片）
        if (this.isFillQues(item.typeId)) {
          let $dom = $(`#${item.id} .imglist-content`);
          item.editContent = $dom.html();
        }
        if (item.children) delete item.children;
        outputList.push(item);
      }
    });

    // console.log('outputList:2', outputList);
    this.smallQuesNo = 1;
    outputList.forEach((item: any, index: number) => {
      item.Index = index;

      // 为兼容题型识别，混合题保存为简答题，并添加标记，
      if (item.typeId == QUES_TYPE.mixin) {
        item.typeId = QUES_TYPE.subject;
        item.isMixinQuesType = true;

        // 保存小题目文本
        item.data.forEach((subItem: any) => {
          let $qDom = document.getElementById(subItem.id);
          if (subItem.typeId == QUES_TYPE.subject) {
            $qDom = document.getElementById(subItem.data[0].id);
          }
          if ($qDom) {
            let $smallTitle: any = $qDom.querySelectorAll(
              ".ques-content-name--small"
            );
            if ($smallTitle.length) {
              subItem.quesFullName = $smallTitle[0].innerText;
              subItem.hideSmallName = !subItem.quesFullName;
            }
          }
        });
      }

      // 标记小题序号
      this.markSmallQuseNo(item, index);
      // 保存自定义标题到name字段
      this.saveCustionQuesName(item);
    });

    return outputList;
  }

  /**
   * @name 附加简答题html内容
   */
  private addSubjectHtmlContent(subItem: any) {
    const doms = document.querySelectorAll(
      `[data-tag="subjectbox_${subItem.id}"]`
    );
    let thtml = "";
    // 只获取第一
    // let dom = doms[0];
    Array.from(doms).forEach((dom, index) => {
      if (!dom) return;

      const _dom: any = dom.cloneNode(true);
      _dom.innerHTML = _dom.innerHTML.replace(/remove-ques/g, "");
      const deletDom = [
        "noeditsave",
        // "remove-node",
        "cke_widget_drag_handler_container",
        "cke_image_resizer",
      ];
      deletDom.forEach((el) => {
        Array.from((_dom as HTMLElement)?.getElementsByClassName(el)).forEach(
          (item: any) => {
            //获取自身节点的父节点
            const parent = item.parentElement;
            //删除节点
            parent.removeChild(item);
          }
        );
      });
      Array.from(_dom.getElementsByClassName("ck-math-tex")).forEach(
        (item: any) => {
          let span = document.createElement("span");
          span.innerHTML = item.getAttribute("tex");
          item.parentElement.replaceChild(span, item);
        }
      );

      thtml += _dom.innerHTML;
    })

    subItem.editContent = thtml;
  }

  /**
   * @name 导出需要保存的题目数据到JSON
   */
  public exportSaveJSON() {
    // let savedJson = []
    let savedJson = this.getSaveCardContent();
    const cardObj = {
      correctType: this.correctType,
      pageLayout: this.pageLayout,
      cardType: this.cardType,
      isSealLine: this.isSealLine,
      isNumberHasX: this.isNumberHasX,
      fontSize: this.fontSize,
      space: this.space,
      headerName: this.headerName,
      fontFamily: this.fontFamily,
      html: this.getSaveHtml(),
      title: this.name,
      nameHtml: this.nameHtml,
      // ansToTop: this.ansToTop,
      ansToTopConfig: this.ansToTopConfig,
      ansType: this.ansType,
      optionFontSize: this.optionFontSize,
      mergeQues: this.mergeQues,
      numberLayout: this.numberLayout,
      spreadQues: 1,
      zoom: getBrowerZoom(),
    };

    let subjectName = '';
    // 是否多学科
    if (this.multiSubjects.length) {
      subjectName = this.multiSubjects.map(item => item.subjectName).join(',');
    } else {
      subjectName = this.subjectList.filter((item: any) => {
        return item.subjectId == this.subjectId;
      })[0].subjectName;
    }

    let params = {
      paperNo: this.paperId,
      cardInfo: cardObj,
      quesInfo: savedJson,
      groupImgData: JSON.stringify(Array.from(this.groupImgData.entries())),
      cardQueInfo: this.cardType != ICARDMODEL.ENGLISH && this.getTempHtml(),
      cardType: this.cardType,
      correctType: this.correctType,
      pageLayout: this.pageLayout,
      isTemplate: this.isTemplate,
      paperNum: this.paperNum || 1,
      subjectCode: this.subjectId,
      subjectName: subjectName,
      stuNoLength: this.stuNoLength,
      abCardType: this.abType ? 1 : this.isABPaper ? 2 : 0,
    };
    return params;
  }

  /**
   * @name 补充题卡合一混合题中题型缺失的字段
   */
  private fillQUESCARDMixinMissing(sq: ISmallQues) {
    // 补充混合题卡中的题型参数
    if (sq.answer && typeof sq.answer !== "string") {
      sq.answer = sq.answer.toString();
    }

    // 补充判断题的判断类型
    if (sq.typeId == QUES_TYPE.judge) {
      sq.judgeType = this.judgeType;
    }

    // 补充填空题类型
    if (this.isFillQues(sq.typeId)) {
      // 混合体内不应该存在智批题，强制修改
      sq.typeId = QUES_TYPE.fill;
      sq.step = sq.step || this.step;
      sq.markType = sq.markType || this.markType;
      sq.gridPlace = sq.gridPlace || GRIDPLACE_TYPE.AFTER;
      sq.lineType = sq.lineType || this.lineType;
      sq.lineNum = sq.lineNum || this.lineNum;
      if (sq.lineList && sq.lineList.length) sq.rendered = true;
    }
    // TIP: 加工系统存在二级混合题，这里做一个临时标记，防止多加层级
    sq.maxLevel = sq.data ? 3 : 2;
  }

  /**
   * @name 将3级题型转换为2级题型
   * @description 仅题卡合一使用
   */
  public convertQuesLevel3to2(quesInfos: any[]) {
    let quesMap: any = {};
    quesInfos.forEach((item: any, idx: number) => {
      item.showName = item.showName || "1";
      item.markType = item.markType || this.markType;
      item.gridType = item.gridType || this.gridType;
      item.decimal = item.decimal || this.decimal;
      if (
        item.name == item.type &&
        !/^[一二三四五六七八九十]+[、.．]/.test(item.name) && item.typeId != QUES_TYPE.noAnswerArea
      ) {
        item.name = arabToChinese(Number(idx + 1)) + "、" + item.name;
      }
      // if (item.quesScore == "") {
      //   item.quesScore = item.score / item.count;
      // }
      if (item.typeId == QUES_TYPE.choice && !item.halfScore) {
        item.halfScore = item.score / item.count / 2;
        item.ruleType = IRULERTYPE.STANDAD;
      }

      if (item.data[0].data) {
        let bigQues = item.data[0];
        const obj = this.questions[bigQues.id];
        if (this.questions[bigQues.id]) {
          let isMixin = this.checkQuesIsMixin(bigQues);

          bigQues.data.forEach((sq: any, i: number) => {
            if (isMixin) {
              // 补充混合题卡中的题型参数
              this.fillQUESCARDMixinMissing(sq)
            }

            if (item.quesScore != sq.score) {
              item.quesScore = "";
            }
            sq.markType = sq.markType || this.markType;
            sq.gridType = sq.gridType || this.gridType;
            sq.decimal = sq.decimal || this.decimal;
            sq.level = 3;//标识3级题目
            if (sq.typeId == QUES_TYPE.choice && !sq.halfScore) {
              sq.halfScore = item.halfScore;
              sq.ruleType = sq.ruleType || item.ruleType || IRULERTYPE.STANDAD;
            }
            let qs = obj.qsList[i];
            qs.id = sq.id;
            if (i == 0) {
              qs.tmpcontent = obj.content;
              qs.topic = obj.topic + qs.topic;
            }
            qs.qsList = [deepClone(qs)];
            obj.qsList[i].quesNos = qs.quesNos;
            quesMap[qs.id] = qs;
          });
        } else if (this.questions[bigQues.data[0].id]) {
          quesMap = this.questions;
        }
        item.data = item.data[0].data;
      } else {
        let isMixin = this.checkQuesIsMixin(item);
        item.data.forEach((sq: any, i: number) => {
          if (isMixin) {
            this.fillQUESCARDMixinMissing(sq)
          }

          sq.markType = sq.markType || this.markType;
          sq.gridType = sq.gridType || this.gridType;
          sq.decimal = sq.decimal || this.decimal;
          if (item.quesScore != sq.score) {
            item.quesScore = "";
          }
          if (sq.typeId == QUES_TYPE.choice && !sq.halfScore) {
            sq.halfScore = sq.score / 2;
            sq.ruleType = sq.ruleType || IRULERTYPE.STANDAD;
          }
          const obj = this.questions[sq.id];
          if (!obj) return;

          obj.qsList[0].quesNos = sq.quesNos;
          quesMap[sq.id] = obj;
        });
      }
    });
    this.questions = quesMap;

    return quesInfos;
  }

  /**
   * @name 将所有题型转换为扁平列表题型
   * @description 仅答题卡使用
   */
  public convertQuesToFlat(quesInfos: any[]): { list: any[]; ids: string[] } {
    // console.log('convertQuesToFlat:', quesInfos);

    let ids = [];
    let quesNo = 1;
    let flatQuesList = [];
    quesInfos.forEach((bigQues: any, idx: number) => {
      if(bigQues.typeId == QUES_TYPE.noAnswerArea){
        if(!bigQues.data?.length){
          bigQues.data = [
            {
              quesNos: '',
              score: 0,
              name: '',
              typeId: QUES_TYPE.noAnswerArea,
              id: generateUUID(),
              type: '',
              answer: '',
              optionCount: 4,
              isWriting: false
            }
          ];
        }
      }
      // 有isMixinQuesType标记的改为混合题
      if (
        bigQues.typeId == QUES_TYPE.subject &&
        bigQues.isMixinQuesType == true
      ) {
        bigQues.typeId = QUES_TYPE.mixin;
      }

      // 为答题补充id字段
      bigQues.id = bigQues.id || generateUUID();
      bigQues.data.forEach((ques: any) => {
        if (ques.data) {
          ques.data.forEach((sq: any) => {
            sq.quesNo = quesNo++;
            if (sq.typeId == QUES_TYPE.judge) {
              sq.answer = sq.answer?.toString()
            }
          });
        } else {
          ques.quesNo = quesNo++;
          if (ques.typeId == QUES_TYPE.judge) {
            ques.answer = ques.answer?.toString()
          }
        }
        ids.push(ques.id);
      });

      if (bigQues.typeId == QUES_TYPE.mixin) {
        // 混合题型
        flatQuesList.push(bigQues, ...bigQues.data);
        // bigQues.data = [];
      } else {
        // 非混合题型
        if (bigQues.mixinMode) {
          // 混合模式的单题
          let dataList = [];
          let _bigQues = deepClone(bigQues);
          bigQues.data.forEach((subItem) => {
            if (subItem.mixinMode) {
              let parentId = subItem.id;
              dataList.push(
                subItem,
                ...(subItem.data
                  ? subItem.data.filter(
                    (ssubItem) => ssubItem.parentId == parentId
                  )
                  : [])
              );
              delete subItem.data;
            } else {
              dataList.push(subItem);
            }
          });
          _bigQues.data = dataList;
          flatQuesList.push(_bigQues);
        } else {
          // 独立单题
          if (this.isChoiceQues(bigQues.typeId)) {
            bigQues.verticalLines = Number(bigQues.verticalLines) || 5;
          }
          flatQuesList.push(bigQues);
        }
      }
    });

    // console.log('convertQuesToFlat:2', flatQuesList);

    return {
      list: flatQuesList,
      ids,
    };
  }

  /**
   * @description: 将智批改为普通批改
   */
  public cancelAIScan() {
    let hasFillEva = false;
    for (const [index, quesInfo] of this.quesInfosInCard.entries()) {
      if (quesInfo.scanMode != QUES_SCAN_MODE.AI_FILL) continue;
      quesInfo.scanMode = QUES_SCAN_MODE.NORMAL;
      quesInfo.markType = MARK_TYPE.NUMBER;
      for (const data of quesInfo.data) {
        data.scanMode = QUES_SCAN_MODE.NORMAL;
        data.markType = MARK_TYPE.NUMBER;
      }
      if (!hasFillEva) hasFillEva = true;
      this.updateQuestions(index, JSON.parse(JSON.stringify(quesInfo)));
    }

    if (hasFillEva) this.notifyUpdateAllData();
  }

  /**
   * @description: 将填空智批改改为普通填空题
   */
  public fillEvaToFillQues() {
    let hasFillEva = false;
    for (const quesInfo of this.quesInfosInCard) {
      if (quesInfo.typeId != QUES_TYPE.fillEva) continue;
      quesInfo.typeId = QUES_TYPE.fill;
      for (const data of quesInfo.data) {
        data.typeId = QUES_TYPE.fill;
      }
      if (!hasFillEva) hasFillEva = true;
    }

    if (hasFillEva) this.notifyUpdateAllData();
  }

  /**
   * @description: 转换题目名称格式，判断是否去除拼接的分数信息
   */
  public convertQuesName(qTitle: string) {
    const match = qTitle.match(/\((.*?)\)[^\(\)]*$/);
    if (match) {
      let scoreText = match[1];

      qTitle =
        scoreText.length > 3
          ? qTitle
          : qTitle.replace(/\s*[\(（].*?[\)）]\s*$/, "");
    }

    return qTitle;
  }
}

export default new PaperManager();
