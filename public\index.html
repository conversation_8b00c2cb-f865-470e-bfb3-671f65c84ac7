<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-05-16 14:37:08
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-08-15 01:33:47
-->
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="icon" href="<%= VUE_APP_ICO_PATH %>" />
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <style>
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-thumb {
      background-color: #c6c6c6;
      min-height: 25px;
      min-width: 25px;
      /* border: 3px solid #fff; */
      border-radius: 3px;
    }

    .wrs_tickContainer {
      display: none;
    }
  </style>
  <script type="text/javascript" src="<%= VUE_APP_SSO %>"></script>
  <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?15da8d1ed9aa611653b7e1b7289b82ea";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
    
  <!-- <link rel="stylesheet" href="static/rrweb.min.css" /> -->

</head>

<body ondragstart="return false;" ondragover="return false;">
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong>
  </noscript>
  <div id="app"></div>

  <!-- <script src="static/rrweb-all.min.js"></script> -->
  <script src="static/jquery.min.js"></script>
  <script src="static/drag.js"></script>
  <script src="static/ckeditor_4.22.1/ckeditor/ckeditor.js"></script>
  <script src="static/<EMAIL>"></script>
  <script src="https://fs.iclass30.com/aliba/plug/mathjax/MathJax.js" defer></script>
  <script type="text/x-mathjax-config">
    MathJax.Hub.Register.StartupHook("TeX Jax Ready", function () {
      var TEX = MathJax.InputJax.TeX;
      // 注册预处理钩子，在处理 TeX 公式前执行
      TEX.prefilterHooks.Add(function (data) {
          // 判断是否是行内公式（非 display 模式）
          if (!data.display) {
              // 给行内公式前面添加 \displaystyle 
              data.math = '\\displaystyle ' + data.math;
          }
          return data;
      });
  });
    MathJax.Hub.Config({
      showProcessingMessages: false,
      messageStyle: "none",
      menuSettings: {
        context: "Browser"
      },
      "HTML-CSS": {
        webFont: "STIX-Web",
        availableFonts: ["STIX-Web"],
        preferredFont: "STIX-Web",
        styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
        undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
        scale:110
      },
      "CommonHTML": {
        webFont: "STIX-Web",
        availableFonts: ["STIX-Web"],
        preferredFont: "STIX-Web",
        styles: {".wrs_editor .wrs_tickContainer":{display: "none"},".MathJax_Preview": {visibility: "hidden"},".MathJax": {"text-align": "center","margin": "0em 0.3em","padding":"0em","display":"inline-block","transform":"scale(1)"}},
        undefinedFamily: "'Times New Roman', 'Arial Unicode MS', serif",
        scale:110,
        matchFontHeight: false
      },
      tex2jax: {
        inlineMath: [['$$','$$'], ['$','$'],["\\(","\\)"],["\\[", "\\]"]],
        processEscapes: true,
        skipTags: ['script', 'style',  'pre', 'code']
      },
      MMLorHTML: {prefer: "HTML"},
      jax: ["input/TeX","output/CommonHTML"],
      extensions: ["tex2jax.js","mml2jax.js","MathMenu.js","MathZoom.js", "fast-preview.js", "AssistiveMML.js"],
      TeX: {
        extensions: ["extpfeil.js","AMSmath.js","AMSsymbols.js","noErrors.js","noUndefined.js","cancel.js","mhchem.js","autoload-all.js"]
      },
      font:"STIX-Web",

      SVG: {
        scale: 110,
        linebreaks: {
             automatic: true
        },
        addMMLclasses: false
      },
    });
  </script>
</body>

</html>