/**
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-10-30 19:56:34
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2023-12-09 09:05:21
 */
const port = 9801;
const path = require('path');
const webpack = require('webpack');
const isDev = process.env.NODE_ENV === 'development';
const BuildConfig = require('./config/index');
const ConfigEnv = isDev ? 'dev' : 'build';
const dllPath = BuildConfig.dll.path;
const outputDir = BuildConfig.build.assetsRoot;
const { name } = require('./package');
const Timestamp = new Date().getTime();
const pxToVw = require('postcss-px-to-viewport');
let styleVariables = require('./src/style/variables.scss.js');

module.exports = {
    pages: {
        index: {
            // page 的入口
            entry: 'src/main.ts',
            // 模板来源
            template: 'public/index.html',
            // 在 dist/index.html 的输出
            filename: 'index.html',
            //后面根据项目动态获取
            title: '精准教学 - 制卡'
        }
    },
    // 输出路径
    outputDir: outputDir,
    // publicPath: BuildConfig[ConfigEnv].assetsPublicPath,
    publicPath: process.env.BASE_URL,
    indexPath: BuildConfig.build.index,
    productionSourceMap: BuildConfig.build.productionSourceMap,
    // eslint-loader 是否在保存的时候检查
    lintOnSave: false,
    // runtimeCompiler:true,//是否使用包含运行时编译器的 Vue 构建版本
    devServer: {
        hot: true,
        disableHostCheck: true,
        port,
        overlay: {
            warnings: false,
            errors: true,
        },
        headers: {
            'Access-Control-Allow-Origin': '*',
        },
        proxy: BuildConfig.dev.proxyTable
    },
    chainWebpack: (config) => {
        config.optimization.minimizer('terser').tap((args) => {
            args[0].terserOptions.compress = {
                ...args[0].terserOptions.compress,
                pure_funcs: [
                    'console.debug',
                    'console.table',
                ],
            }

            args[0].terserOptions.output = {
                comments: false,
            }
            args[0].extractComments = false
            return args
        })
        config.output
            .library(`${name}-[name]`)
            .libraryTarget('umd')
            .jsonpFunction(`webpackJsonp_${name}`)
            .filename(`static/js/[name].${process.env.VUE_APP_VERSION}.${Timestamp}.js`)
            .chunkFilename(`static/js/[name].${process.env.VUE_APP_VERSION}.${Timestamp}.js`)

        config.module
            .rule('url-loader')
            .test(/\.(cur)(\?.*)?$/)
            .use('url-loader')
            .loader('url-loader')
            .end()
    },
    css: {
        loaderOptions: {
            less: {
                javascriptEnabled: true
            },
            postcss: {
                postcssOptions: {
                    plugins: [
                        require('autoprefixer')
                    ]
                }
            }
        }
    },
    configureWebpack: (config) => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境
            config.mode = 'production'
        } else {
            // 开发环境
            config.mode = 'development'
        }
        if (isDev) {
            // 为开发环境修改配置...
            //devtool用作调试 这种配置会生成一个带有.map文件，这个map文件会和原始文件做一个映射，调试的时候，就是通过这个.map文件去定位原来的代码位置的
            config.devtool = 'source-map';
        } else {
            if (BuildConfig.build.productionGzip) {
                const CompressionWebpackPlugin = require('compression-webpack-plugin');
                // 开启GZIP压缩
                config.plugins.push(new CompressionWebpackPlugin({
                    filename: '[path].gz[query]',
                    algorithm: 'gzip',
                    test: new RegExp('\\.(' + BuildConfig.build.productionGzipExtensions.join('|') + ')$'),
                    threshold: 10240,
                    minRatio: 0.8
                }));
            }
            if (process.env.use_analyzer === 'true') {
                const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
                // 生成分析报告
                config.plugins.push(new BundleAnalyzerPlugin());
            }
        }

        plugins: [
            new webpack.ProvidePlugin({

                $: "jquery",

                jQuery: "jquery",

                "windows.jQuery": "jquery"

            }),
        ]
    }
};

function resolve(dir) {
    return path.join(__dirname, dir);
}

// 获取dll文件的manifest
function getDllManifest() {
    var plugins = [];
    Object.keys(BuildConfig.dll.entry).forEach((name) => {
        plugins.push(
            new webpack.DllReferencePlugin({
                context: process.cwd(),
                manifest: path.resolve(`./${dllPath}/${name}-manifest.json`)
            })
        );
    });
    return plugins;
}


